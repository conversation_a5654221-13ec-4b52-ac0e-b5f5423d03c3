{"version": "1.4.0", "project": {"name": "dependencyProducer", "directory": "D:\\code\\feature-metasdk", "workspaceDir": "D:\\code\\feature-metasdk", "sources": ["D:\\code\\feature-metasdk\\src\\main\\java", "D:\\code\\feature-metasdk\\src\\main\\scala"], "dependencies": [], "classpath": ["D:\\code\\feature-metasdk\\target\\classes", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-talos-sdk\\2.7.0.2\\galaxy-talos-sdk-2.7.0.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-lcs-common\\2.1.13\\galaxy-lcs-common-2.1.13.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-lcs-thrift\\2.1.13\\galaxy-lcs-thrift-2.1.13.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\guava\\11.0.2\\guava-11.0.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\findbugs\\jsr305\\1.3.9\\jsr305-1.3.9.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\googlecode\\concurrentlinkedhashmap\\concurrentlinkedhashmap-lru\\1.4.2\\concurrentlinkedhashmap-lru-1.4.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\dnsjava\\dnsjava\\2.1.8\\dnsjava-2.1.8.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\json\\json\\20090211\\json-20090211.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util\\0.0.92\\util-0.0.92.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util-executor-service-shutdown\\0.0.47\\util-executor-service-shutdown-0.0.47.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\jdk-logging\\0.0.41\\jdk-logging-0.0.41.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\base\\0.0.82\\base-0.0.82.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util-system-mocks\\0.0.67\\util-system-mocks-0.0.67.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\collections\\0.0.69\\collections-0.0.69.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\quantity\\0.0.66\\quantity-0.0.66.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stats\\0.0.89\\stats-0.0.89.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stat-registry\\0.0.24\\stat-registry-0.0.24.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stat\\0.0.26\\stat-0.0.26.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stats-provider\\0.0.53\\stats-provider-0.0.53.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\application-action\\0.0.66\\application-action-0.0.66.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\inject\\guice\\3.0\\guice-3.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\aopalliance\\aopalliance\\1.0\\aopalliance-1.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util-sampler\\0.0.50\\util-sampler-0.0.50.jar", "C:\\Users\\<USER>\\.m2\\repository\\javax\\inject\\javax.inject\\1\\javax.inject-1.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\gson\\gson\\2.2.4\\gson-2.2.4.jar", "C:\\Users\\<USER>\\.m2\\repository\\javax\\servlet\\javax.servlet-api\\3.0.1\\javax.servlet-api-3.0.1.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-lcs-metric-lib\\2.1.13\\galaxy-lcs-metric-lib-2.1.13.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\codahale\\metrics\\metrics-core\\3.0.2\\metrics-core-3.0.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-client-java\\1.2.5\\galaxy-client-java-1.2.5.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-log4j12\\1.6.1\\slf4j-log4j12-1.6.1.jar", "C:\\Users\\<USER>\\.m2\\repository\\log4j\\log4j\\1.2.16\\log4j-1.2.16.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\uuid\\java-uuid-generator\\3.1.3\\java-uuid-generator-3.1.3.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-thrift-api\\1.2.8\\galaxy-thrift-api-1.2.8.jar", "C:\\Users\\<USER>\\.m2\\repository\\commons-codec\\commons-codec\\1.4\\commons-codec-1.4.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-lang3\\3.1\\commons-lang3-3.1.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\xerial\\snappy\\snappy-java\\1.1.2.6\\snappy-java-1.1.2.6.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\github\\luben\\zstd-jni\\1.5.0-4\\zstd-jni-1.5.0-4.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\lz4\\lz4-java\\1.8.0\\lz4-java-1.8.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpclient\\4.2.2\\httpclient-4.2.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\commons-logging\\commons-logging\\1.1.1\\commons-logging-1.1.1.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpcore\\4.2.2\\httpcore-4.2.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-schema-client\\1.0.0\\galaxy-schema-client-1.0.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\streaming-common\\1.0\\streaming-common-1.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\streaming-http\\1.0\\streaming-http-1.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\avro\\avro\\1.8.2\\avro-1.8.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\jackson\\jackson-core-asl\\1.9.13\\jackson-core-asl-1.9.13.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\jackson\\jackson-mapper-asl\\1.9.13\\jackson-mapper-asl-1.9.13.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\thoughtworks\\paranamer\\paranamer\\2.7\\paranamer-2.7.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-compress\\1.8.1\\commons-compress-1.8.1.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\tukaani\\xz\\1.5\\xz-1.5.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\avro\\avro-thrift\\1.8.2\\avro-thrift-1.8.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\avro\\avro-protobuf\\1.8.2\\avro-protobuf-1.8.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\protobuf\\protobuf-java\\2.5.0\\protobuf-java-2.5.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-schema-thrift\\1.0.0\\galaxy-schema-thrift-1.0.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\commons-beanutils\\commons-beanutils\\1.9.4\\commons-beanutils-1.9.4.jar", "C:\\Users\\<USER>\\.m2\\repository\\commons-collections\\commons-collections\\3.2.2\\commons-collections-3.2.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\jsoup\\jsoup\\1.13.1\\jsoup-1.13.1.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\thrift\\libthrift\\0.15.0\\libthrift-0.15.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-api\\1.7.28\\slf4j-api-1.7.28.jar", "C:\\Users\\<USER>\\.m2\\repository\\javax\\annotation\\javax.annotation-api\\1.3.2\\javax.annotation-api-1.3.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\data\\data-platform-spec-feeds\\0.0.1-SNAPSHOT\\data-platform-spec-feeds-0.0.1-SNAPSHOT.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\thrift\\thrift\\0.5.0-fix-thrift2402\\thrift-0.5.0-fix-thrift2402.jar", "C:\\Users\\<USER>\\.m2\\repository\\commons-lang\\commons-lang\\2.6\\commons-lang-2.6.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-databind\\2.15.2\\jackson-databind-2.15.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-annotations\\2.15.2\\jackson-annotations-2.15.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-core\\2.15.2\\jackson-core-2.15.2.jar", "C:\\Users\\<USER>\\.m2\\repository\\commons-io\\commons-io\\2.11.0\\commons-io-2.11.0.jar", "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-simple\\1.7.36\\slf4j-simple-1.7.36.jar"], "out": "D:\\code\\feature-metasdk\\target", "classesDir": "D:\\code\\feature-metasdk\\target\\classes", "resources": ["D:\\code\\feature-metasdk\\src\\main\\resources"], "java": {"options": ["-g", "-target", "8", "-source", "8", "-encoding", "UTF-8"]}, "test": {"frameworks": [{"names": ["com.novocode.junit.JUnitFramework"]}, {"names": ["org.scalatest.tools.Framework", "org.scalatest.tools.ScalaTestFramework"]}, {"names": ["org.scalacheck.ScalaCheckFramework"]}, {"names": ["org.specs.runner.SpecsFramework", "org.specs2.runner.Specs2Framework", "org.specs2.runner.SpecsFramework"]}, {"names": ["utest.runner.Framework"]}, {"names": ["munit.Framework"]}], "options": {"excludes": [], "arguments": [{"args": ["-v", "-a"], "framework": {"names": ["com.novocode.junit.JUnitFramework"]}}]}}, "platform": {"name": "jvm", "config": {"home": "C:\\Users\\<USER>\\AppData\\Local\\Coursier\\cache\\arc\\https\\github.com\\adoptium\\temurin17-binaries\\releases\\download\\jdk-17.0.14%252B7\\OpenJDK17U-jdk_x64_windows_hotspot_17.0.14_7.zip\\jdk-17.0.14+7", "options": []}, "mainClass": []}, "resolution": {"modules": [{"organization": "com.fasterxml.jackson.core", "name": "jackson-core", "version": "2.15.2", "artifacts": [{"name": "jackson-core", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-core\\2.15.2\\jackson-core-2.15.2.jar"}, {"name": "jackson-core", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-core\\2.15.2\\jackson-core-2.15.2-sources.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "galaxy-client-java", "version": "1.2.5", "artifacts": [{"name": "galaxy-client-java", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-client-java\\1.2.5\\galaxy-client-java-1.2.5.jar"}, {"name": "galaxy-client-java", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-client-java\\1.2.5\\galaxy-client-java-1.2.5-sources.jar"}]}, {"organization": "javax.servlet", "name": "javax.servlet-api", "version": "3.0.1", "artifacts": [{"name": "javax.servlet-api", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\servlet\\javax.servlet-api\\3.0.1\\javax.servlet-api-3.0.1.jar"}, {"name": "javax.servlet-api", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\servlet\\javax.servlet-api\\3.0.1\\javax.servlet-api-3.0.1-sources.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "galaxy-lcs-common", "version": "2.1.13", "artifacts": [{"name": "galaxy-lcs-common", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-lcs-common\\2.1.13\\galaxy-lcs-common-2.1.13.jar"}]}, {"organization": "org.slf4j", "name": "slf4j-api", "version": "1.7.28", "artifacts": [{"name": "slf4j-api", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-api\\1.7.28\\slf4j-api-1.7.28.jar"}, {"name": "slf4j-api", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-api\\1.7.28\\slf4j-api-1.7.28-sources.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "galaxy-schema-thrift", "version": "1.0.0", "artifacts": [{"name": "galaxy-schema-thrift", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-schema-thrift\\1.0.0\\galaxy-schema-thrift-1.0.0.jar"}]}, {"organization": "org.slf4j", "name": "slf4j-simple", "version": "1.7.36", "artifacts": [{"name": "slf4j-simple", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-simple\\1.7.36\\slf4j-simple-1.7.36.jar"}, {"name": "slf4j-simple", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-simple\\1.7.36\\slf4j-simple-1.7.36-sources.jar"}]}, {"organization": "com.xiaomi.data", "name": "data-platform-spec-feeds", "version": "0.0.1-SNAPSHOT", "artifacts": [{"name": "data-platform-spec-feeds", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\data\\data-platform-spec-feeds\\0.0.1-SNAPSHOT\\data-platform-spec-feeds-0.0.1-SNAPSHOT.jar"}, {"name": "data-platform-spec-feeds", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\data\\data-platform-spec-feeds\\0.0.1-SNAPSHOT\\data-platform-spec-feeds-0.0.1-SNAPSHOT-sources.jar"}]}, {"organization": "org.apache.commons", "name": "commons-lang3", "version": "3.1", "artifacts": [{"name": "commons-lang3", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-lang3\\3.1\\commons-lang3-3.1.jar"}, {"name": "commons-lang3", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-lang3\\3.1\\commons-lang3-3.1-sources.jar"}]}, {"organization": "org.codehaus.jackson", "name": "jackson-core-asl", "version": "1.9.13", "artifacts": [{"name": "jackson-core-asl", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\jackson\\jackson-core-asl\\1.9.13\\jackson-core-asl-1.9.13.jar"}, {"name": "jackson-core-asl", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\jackson\\jackson-core-asl\\1.9.13\\jackson-core-asl-1.9.13-sources.jar"}]}, {"organization": "com.fasterxml.jackson.core", "name": "jackson-databind", "version": "2.15.2", "artifacts": [{"name": "jackson-databind", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-databind\\2.15.2\\jackson-databind-2.15.2.jar"}, {"name": "jackson-databind", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-databind\\2.15.2\\jackson-databind-2.15.2-sources.jar"}]}, {"organization": "javax.annotation", "name": "javax.annotation-api", "version": "1.3.2", "artifacts": [{"name": "javax.annotation-api", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\annotation\\javax.annotation-api\\1.3.2\\javax.annotation-api-1.3.2.jar"}, {"name": "javax.annotation-api", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\annotation\\javax.annotation-api\\1.3.2\\javax.annotation-api-1.3.2-sources.jar"}]}, {"organization": "org.apache.avro", "name": "avro-thrift", "version": "1.8.2", "artifacts": [{"name": "avro-thrift", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\avro\\avro-thrift\\1.8.2\\avro-thrift-1.8.2.jar"}, {"name": "avro-thrift", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\avro\\avro-thrift\\1.8.2\\avro-thrift-1.8.2-sources.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "streaming-common", "version": "1.0", "artifacts": [{"name": "streaming-common", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\streaming-common\\1.0\\streaming-common-1.0.jar"}]}, {"organization": "com.twitter.common", "name": "stats", "version": "0.0.89", "artifacts": [{"name": "stats", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stats\\0.0.89\\stats-0.0.89.jar"}, {"name": "stats", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stats\\0.0.89\\stats-0.0.89-sources.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "streaming-http", "version": "1.0", "artifacts": [{"name": "streaming-http", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\streaming-http\\1.0\\streaming-http-1.0.jar"}]}, {"organization": "commons-codec", "name": "commons-codec", "version": "1.4", "artifacts": [{"name": "commons-codec", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-codec\\commons-codec\\1.4\\commons-codec-1.4.jar"}, {"name": "commons-codec", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-codec\\commons-codec\\1.4\\commons-codec-1.4-sources.jar"}]}, {"organization": "com.codahale.metrics", "name": "metrics-core", "version": "3.0.2", "artifacts": [{"name": "metrics-core", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\codahale\\metrics\\metrics-core\\3.0.2\\metrics-core-3.0.2.jar"}, {"name": "metrics-core", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\codahale\\metrics\\metrics-core\\3.0.2\\metrics-core-3.0.2-sources.jar"}]}, {"organization": "com.twitter.common", "name": "stats-provider", "version": "0.0.53", "artifacts": [{"name": "stats-provider", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stats-provider\\0.0.53\\stats-provider-0.0.53.jar"}, {"name": "stats-provider", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stats-provider\\0.0.53\\stats-provider-0.0.53-sources.jar"}]}, {"organization": "com.twitter.common", "name": "util-system-mocks", "version": "0.0.67", "artifacts": [{"name": "util-system-mocks", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util-system-mocks\\0.0.67\\util-system-mocks-0.0.67.jar"}, {"name": "util-system-mocks", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util-system-mocks\\0.0.67\\util-system-mocks-0.0.67-sources.jar"}]}, {"organization": "org.apache.avro", "name": "avro-protobuf", "version": "1.8.2", "artifacts": [{"name": "avro-protobuf", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\avro\\avro-protobuf\\1.8.2\\avro-protobuf-1.8.2.jar"}, {"name": "avro-protobuf", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\avro\\avro-protobuf\\1.8.2\\avro-protobuf-1.8.2-sources.jar"}]}, {"organization": "org.apache.thrift", "name": "thrift", "version": "0.5.0-fix-thrift2402", "artifacts": [{"name": "thrift", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\thrift\\thrift\\0.5.0-fix-thrift2402\\thrift-0.5.0-fix-thrift2402.jar"}, {"name": "thrift", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\thrift\\thrift\\0.5.0-fix-thrift2402\\thrift-0.5.0-fix-thrift2402-sources.jar"}]}, {"organization": "log4j", "name": "log4j", "version": "1.2.16", "artifacts": [{"name": "log4j", "path": "C:\\Users\\<USER>\\.m2\\repository\\log4j\\log4j\\1.2.16\\log4j-1.2.16.jar"}, {"name": "log4j", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\log4j\\log4j\\1.2.16\\log4j-1.2.16-sources.jar"}]}, {"organization": "dnsjava", "name": "dnsjava", "version": "2.1.8", "artifacts": [{"name": "dnsjava", "path": "C:\\Users\\<USER>\\.m2\\repository\\dnsjava\\dnsjava\\2.1.8\\dnsjava-2.1.8.jar"}, {"name": "dnsjava", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\dnsjava\\dnsjava\\2.1.8\\dnsjava-2.1.8-sources.jar"}]}, {"organization": "org.apache.httpcomponents", "name": "httpcore", "version": "4.2.2", "artifacts": [{"name": "httpcore", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpcore\\4.2.2\\httpcore-4.2.2.jar"}, {"name": "httpcore", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpcore\\4.2.2\\httpcore-4.2.2-sources.jar"}]}, {"organization": "org.jsoup", "name": "jsoup", "version": "1.13.1", "artifacts": [{"name": "jsoup", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\jsoup\\jsoup\\1.13.1\\jsoup-1.13.1.jar"}, {"name": "jsoup", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\jsoup\\jsoup\\1.13.1\\jsoup-1.13.1-sources.jar"}]}, {"organization": "com.twitter.common", "name": "quantity", "version": "0.0.66", "artifacts": [{"name": "quantity", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\quantity\\0.0.66\\quantity-0.0.66.jar"}, {"name": "quantity", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\quantity\\0.0.66\\quantity-0.0.66-sources.jar"}]}, {"organization": "com.twitter.common", "name": "util", "version": "0.0.92", "artifacts": [{"name": "util", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util\\0.0.92\\util-0.0.92.jar"}, {"name": "util", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util\\0.0.92\\util-0.0.92-sources.jar"}]}, {"organization": "aopalliance", "name": "aopalliance", "version": "1.0", "artifacts": [{"name": "aopalliance", "path": "C:\\Users\\<USER>\\.m2\\repository\\aopalliance\\aopalliance\\1.0\\aopalliance-1.0.jar"}, {"name": "aopalliance", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\aopalliance\\aopalliance\\1.0\\aopalliance-1.0-sources.jar"}]}, {"organization": "org.apache.thrift", "name": "libthrift", "version": "0.15.0", "artifacts": [{"name": "libthrift", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\thrift\\libthrift\\0.15.0\\libthrift-0.15.0.jar"}, {"name": "libthrift", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\thrift\\libthrift\\0.15.0\\libthrift-0.15.0-sources.jar"}]}, {"organization": "com.twitter.common", "name": "stat", "version": "0.0.26", "artifacts": [{"name": "stat", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stat\\0.0.26\\stat-0.0.26.jar"}, {"name": "stat", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stat\\0.0.26\\stat-0.0.26-sources.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "galaxy-lcs-metric-lib", "version": "2.1.13", "artifacts": [{"name": "galaxy-lcs-metric-lib", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-lcs-metric-lib\\2.1.13\\galaxy-lcs-metric-lib-2.1.13.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "galaxy-lcs-thrift", "version": "2.1.13", "artifacts": [{"name": "galaxy-lcs-thrift", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-lcs-thrift\\2.1.13\\galaxy-lcs-thrift-2.1.13.jar"}]}, {"organization": "com.twitter.common", "name": "util-executor-service-shutdown", "version": "0.0.47", "artifacts": [{"name": "util-executor-service-shutdown", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util-executor-service-shutdown\\0.0.47\\util-executor-service-shutdown-0.0.47.jar"}, {"name": "util-executor-service-shutdown", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util-executor-service-shutdown\\0.0.47\\util-executor-service-shutdown-0.0.47-sources.jar"}]}, {"organization": "com.fasterxml.jackson.core", "name": "jackson-annotations", "version": "2.15.2", "artifacts": [{"name": "jackson-annotations", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-annotations\\2.15.2\\jackson-annotations-2.15.2.jar"}, {"name": "jackson-annotations", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-annotations\\2.15.2\\jackson-annotations-2.15.2-sources.jar"}]}, {"organization": "com.twitter.common", "name": "util-sampler", "version": "0.0.50", "artifacts": [{"name": "util-sampler", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util-sampler\\0.0.50\\util-sampler-0.0.50.jar"}, {"name": "util-sampler", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\util-sampler\\0.0.50\\util-sampler-0.0.50-sources.jar"}]}, {"organization": "org.xerial.snappy", "name": "snappy-java", "version": "1.1.2.6", "artifacts": [{"name": "snappy-java", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\xerial\\snappy\\snappy-java\\1.1.2.6\\snappy-java-1.1.2.6.jar"}, {"name": "snappy-java", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\xerial\\snappy\\snappy-java\\1.1.2.6\\snappy-java-1.1.2.6-sources.jar"}]}, {"organization": "commons-beanutils", "name": "commons-beanutils", "version": "1.9.4", "artifacts": [{"name": "commons-beanutils", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-beanutils\\commons-beanutils\\1.9.4\\commons-beanutils-1.9.4.jar"}, {"name": "commons-beanutils", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-beanutils\\commons-beanutils\\1.9.4\\commons-beanutils-1.9.4-sources.jar"}]}, {"organization": "javax.inject", "name": "javax.inject", "version": "1", "artifacts": [{"name": "javax.inject", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\inject\\javax.inject\\1\\javax.inject-1.jar"}, {"name": "javax.inject", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\inject\\javax.inject\\1\\javax.inject-1-sources.jar"}]}, {"organization": "org.codehaus.jackson", "name": "jackson-mapper-asl", "version": "1.9.13", "artifacts": [{"name": "jackson-mapper-asl", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\jackson\\jackson-mapper-asl\\1.9.13\\jackson-mapper-asl-1.9.13.jar"}, {"name": "jackson-mapper-asl", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\jackson\\jackson-mapper-asl\\1.9.13\\jackson-mapper-asl-1.9.13-sources.jar"}]}, {"organization": "com.google.inject", "name": "guice", "version": "3.0", "artifacts": [{"name": "guice", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\inject\\guice\\3.0\\guice-3.0.jar"}, {"name": "guice", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\inject\\guice\\3.0\\guice-3.0-sources.jar"}]}, {"organization": "com.twitter.common", "name": "application-action", "version": "0.0.66", "artifacts": [{"name": "application-action", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\application-action\\0.0.66\\application-action-0.0.66.jar"}, {"name": "application-action", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\application-action\\0.0.66\\application-action-0.0.66-sources.jar"}]}, {"organization": "com.googlecode.concurrentlinkedhashmap", "name": "concurrentlinkedhashmap-lru", "version": "1.4.2", "artifacts": [{"name": "concurrentlinkedhashmap-lru", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\googlecode\\concurrentlinkedhashmap\\concurrentlinkedhashmap-lru\\1.4.2\\concurrentlinkedhashmap-lru-1.4.2.jar"}, {"name": "concurrentlinkedhashmap-lru", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\googlecode\\concurrentlinkedhashmap\\concurrentlinkedhashmap-lru\\1.4.2\\concurrentlinkedhashmap-lru-1.4.2-sources.jar"}]}, {"organization": "com.thoughtworks.paranamer", "name": "paranamer", "version": "2.7", "artifacts": [{"name": "paranamer", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\thoughtworks\\paranamer\\paranamer\\2.7\\paranamer-2.7.jar"}, {"name": "paranamer", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\thoughtworks\\paranamer\\paranamer\\2.7\\paranamer-2.7-sources.jar"}]}, {"organization": "org.apache.avro", "name": "avro", "version": "1.8.2", "artifacts": [{"name": "avro", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\avro\\avro\\1.8.2\\avro-1.8.2.jar"}, {"name": "avro", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\avro\\avro\\1.8.2\\avro-1.8.2-sources.jar"}]}, {"organization": "org.apache.httpcomponents", "name": "httpclient", "version": "4.2.2", "artifacts": [{"name": "httpclient", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpclient\\4.2.2\\httpclient-4.2.2.jar"}, {"name": "httpclient", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpclient\\4.2.2\\httpclient-4.2.2-sources.jar"}]}, {"organization": "com.twitter.common", "name": "base", "version": "0.0.82", "artifacts": [{"name": "base", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\base\\0.0.82\\base-0.0.82.jar"}, {"name": "base", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\base\\0.0.82\\base-0.0.82-sources.jar"}]}, {"organization": "com.google.code.findbugs", "name": "jsr305", "version": "1.3.9", "artifacts": [{"name": "jsr305", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\findbugs\\jsr305\\1.3.9\\jsr305-1.3.9.jar"}]}, {"organization": "org.slf4j", "name": "slf4j-log4j12", "version": "1.6.1", "artifacts": [{"name": "slf4j-log4j12", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-log4j12\\1.6.1\\slf4j-log4j12-1.6.1.jar"}, {"name": "slf4j-log4j12", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-log4j12\\1.6.1\\slf4j-log4j12-1.6.1-sources.jar"}]}, {"organization": "com.google.guava", "name": "guava", "version": "11.0.2", "artifacts": [{"name": "guava", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\guava\\11.0.2\\guava-11.0.2.jar"}, {"name": "guava", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\guava\\11.0.2\\guava-11.0.2-sources.jar"}]}, {"organization": "org.lz4", "name": "lz4-java", "version": "1.8.0", "artifacts": [{"name": "lz4-java", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\lz4\\lz4-java\\1.8.0\\lz4-java-1.8.0.jar"}, {"name": "lz4-java", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\lz4\\lz4-java\\1.8.0\\lz4-java-1.8.0-sources.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "galaxy-schema-client", "version": "1.0.0", "artifacts": [{"name": "galaxy-schema-client", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-schema-client\\1.0.0\\galaxy-schema-client-1.0.0.jar"}]}, {"organization": "commons-io", "name": "commons-io", "version": "2.11.0", "artifacts": [{"name": "commons-io", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-io\\commons-io\\2.11.0\\commons-io-2.11.0.jar"}, {"name": "commons-io", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-io\\commons-io\\2.11.0\\commons-io-2.11.0-sources.jar"}]}, {"organization": "com.google.code.gson", "name": "gson", "version": "2.2.4", "artifacts": [{"name": "gson", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\gson\\gson\\2.2.4\\gson-2.2.4.jar"}, {"name": "gson", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\gson\\gson\\2.2.4\\gson-2.2.4-sources.jar"}]}, {"organization": "commons-logging", "name": "commons-logging", "version": "1.1.1", "artifacts": [{"name": "commons-logging", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-logging\\commons-logging\\1.1.1\\commons-logging-1.1.1.jar"}, {"name": "commons-logging", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-logging\\commons-logging\\1.1.1\\commons-logging-1.1.1-sources.jar"}]}, {"organization": "commons-collections", "name": "commons-collections", "version": "3.2.2", "artifacts": [{"name": "commons-collections", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-collections\\commons-collections\\3.2.2\\commons-collections-3.2.2.jar"}, {"name": "commons-collections", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-collections\\commons-collections\\3.2.2\\commons-collections-3.2.2-sources.jar"}]}, {"organization": "com.github.luben", "name": "zstd-jni", "version": "1.5.0-4", "artifacts": [{"name": "zstd-jni", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\github\\luben\\zstd-jni\\1.5.0-4\\zstd-jni-1.5.0-4.jar"}, {"name": "zstd-jni", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\github\\luben\\zstd-jni\\1.5.0-4\\zstd-jni-1.5.0-4-sources.jar"}]}, {"organization": "org.json", "name": "json", "version": "20090211", "artifacts": [{"name": "json", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\json\\json\\20090211\\json-20090211.jar"}, {"name": "json", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\json\\json\\20090211\\json-20090211-sources.jar"}]}, {"organization": "com.fasterxml.uuid", "name": "java-uuid-generator", "version": "3.1.3", "artifacts": [{"name": "java-uuid-generator", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\uuid\\java-uuid-generator\\3.1.3\\java-uuid-generator-3.1.3.jar"}, {"name": "java-uuid-generator", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\uuid\\java-uuid-generator\\3.1.3\\java-uuid-generator-3.1.3-sources.jar"}]}, {"organization": "com.twitter.common", "name": "jdk-logging", "version": "0.0.41", "artifacts": [{"name": "jdk-logging", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\jdk-logging\\0.0.41\\jdk-logging-0.0.41.jar"}, {"name": "jdk-logging", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\jdk-logging\\0.0.41\\jdk-logging-0.0.41-sources.jar"}]}, {"organization": "org.tukaani", "name": "xz", "version": "1.5", "artifacts": [{"name": "xz", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\tukaani\\xz\\1.5\\xz-1.5.jar"}, {"name": "xz", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\tukaani\\xz\\1.5\\xz-1.5-sources.jar"}]}, {"organization": "com.twitter.common", "name": "collections", "version": "0.0.69", "artifacts": [{"name": "collections", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\collections\\0.0.69\\collections-0.0.69.jar"}, {"name": "collections", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\collections\\0.0.69\\collections-0.0.69-sources.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "galaxy-thrift-api", "version": "1.2.8", "artifacts": [{"name": "galaxy-thrift-api", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-thrift-api\\1.2.8\\galaxy-thrift-api-1.2.8.jar"}, {"name": "galaxy-thrift-api", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-thrift-api\\1.2.8\\galaxy-thrift-api-1.2.8-sources.jar"}]}, {"organization": "com.xiaomi.infra.galaxy", "name": "galaxy-talos-sdk", "version": "2.7.0.2", "artifacts": [{"name": "galaxy-talos-sdk", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-talos-sdk\\2.7.0.2\\galaxy-talos-sdk-2.7.0.2.jar"}, {"name": "galaxy-talos-sdk", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\xiaomi\\infra\\galaxy\\galaxy-talos-sdk\\2.7.0.2\\galaxy-talos-sdk-2.7.0.2-sources.jar"}]}, {"organization": "org.apache.commons", "name": "commons-compress", "version": "1.8.1", "artifacts": [{"name": "commons-compress", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-compress\\1.8.1\\commons-compress-1.8.1.jar"}, {"name": "commons-compress", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-compress\\1.8.1\\commons-compress-1.8.1-sources.jar"}]}, {"organization": "com.twitter.common", "name": "stat-registry", "version": "0.0.24", "artifacts": [{"name": "stat-registry", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stat-registry\\0.0.24\\stat-registry-0.0.24.jar"}, {"name": "stat-registry", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\twitter\\common\\stat-registry\\0.0.24\\stat-registry-0.0.24-sources.jar"}]}, {"organization": "com.google.protobuf", "name": "protobuf-java", "version": "2.5.0", "artifacts": [{"name": "protobuf-java", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\protobuf\\protobuf-java\\2.5.0\\protobuf-java-2.5.0.jar"}, {"name": "protobuf-java", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\protobuf\\protobuf-java\\2.5.0\\protobuf-java-2.5.0-sources.jar"}]}, {"organization": "commons-lang", "name": "commons-lang", "version": "2.6", "artifacts": [{"name": "commons-lang", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-lang\\commons-lang\\2.6\\commons-lang-2.6.jar"}, {"name": "commons-lang", "classifier": "sources", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-lang\\commons-lang\\2.6\\commons-lang-2.6-sources.jar"}]}]}, "tags": ["library"]}}