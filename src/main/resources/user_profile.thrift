namespace java com.xiaomi.data.feeds.dujiangyan 

struct Common {
    1: optional string did; // 国内小米手机设备did（用于关联各项画像）
    2: optional string oaid; // 国内小米手机设备oaid（用户主键）
    3: optional double user_sex;  // 性别(1：男、2：女、null：未知)
    4: optional double user_age_6_level; // 年龄6分段 (1：0-17岁、2：18-24岁等)
    5: optional double user_age_8_level; // 年龄8分段（1：0-17岁、2：18-21岁、3：22-24岁等）
    6: optional double degree_3_level; // 学历（高1.0/中2.0/低3.0 三档）
    7: optional double phone_brand; // 手机品牌（小米2.0、红米1.0、黑鲨3.0等）
    8: optional double phone_series; // 手机系列（小米手机、小米Mix、RedMi K系列等）Redmi K系列1.0；小米Play2.0；小米Note3.0；小米Mix4.0；小米Max5.0；小米手机6.0；红米Pro7.0；红米Note8.0；红米数字9.0；黑鲨10.0；
    9: optional double curr_city_type; // 当前所在城市类型，一线城市1.0；二线城市2.0；三线城市3.0；四线城市4.0；五线城市5.0；新一线城市6.0
    10: optional double phone_retail_price; //手机销售价格
    11: optional double on_office; //上班时间，6-12，9代表(9-10]点之间
    12: optional double is_commuter; //是否是通勤人群，1是通勤人群0不是通勤人群
    13: optional double interest_finance_level; //金融理财兴趣级别
    14: optional double interest_ecom_level; //电商兴趣级别
    15: optional double interest_game_level; //游戏兴趣级别
    16: optional double interest_video_level; //视频兴趣级别
    17: optional double interest_music_level; //音乐兴趣级别
    18: optional double interest_read_level; //阅读兴趣级别
    19: optional double interest_travel_level; //旅游出行兴趣级别
    20: optional double interest_digitaltechnology_level; //科技数码兴趣级别
    21: optional double interest_education_level; //教育培训兴趣级别
    22: optional double phone_usage_30d_cnt; //手机近30天活跃天数
    23: optional double avg_usage_30d; //近30天日均使用时长
    24: optional double has_car; //汽车拥有
    25: optional double has_house; //房产拥有
    26: optional double has_child; //家有儿童(1：有、null: 未知)
    27: optional string resident_country; // 常驻地国家  
    28: optional string resident_province; // 常驻地省份
    29: optional string resident_city; // 常驻地城市(北京、武汉)
    30: optional string resident_district; // 常驻地区县
    31: optional double resident_city_type; // 常驻地城市类型
    32: optional string curr_country; // 当前所在国家
    33: optional string curr_province; // 当前所在省份
    34: optional string curr_city; // 当前所在城市
    35: optional string curr_district; // 当前所在区县
    36: optional string occupation; // 职业
    37: optional map<string, double> fansInfo; // 用户明星粉丝兴趣列表
    38: optional i32 feedsRfmCategory; // 信息流用户类型
}
                                                   
struct SearchRecord {
    1: optional string searchTag;
    2: optional i64 timestamp;
    3: optional i64 totalSearchCnt;
}

struct SearchStats {
    1: optional map<string, double> search_category_last_3day;  //全搜场景小米用户过去3天搜索词对应的category及排序权重
    2: optional map<string, double> search_category_last_week;   //全搜场景小米用户过去一周搜索词对应的category及排序权重
    3: optional map<string, double> search_category_last_month;  // 全搜场景小米用户过去一月搜索词对应的category及排序权重
    4: optional list<double> queryembedding_avgpool;     //全搜场景小米用户过去2周搜索词语义向量的均值池化结果
    5: optional list<string> last20_searchword;       //全搜场景小米用户过去7天的近20个不重复的搜索词（少于20则有多少取多少）
    6: optional list<SearchRecord> browserSearchRecords; // 浏览器场景命中 tag 的搜索词
}
                                                   
                                                     
// 用于存储倒排
// redis key格式为： ${biz}.${queue}.${xxx}
struct InvertValue {
    1: list<string> ids;
    2: optional list<double> scores;
}

struct InvertIndex {
    1: string key;
    2: InvertValue invertValue;
}

struct ItemCount {
    1: string id;
    2: i32 count;
}

struct ItemScore {
    1: string id;
    2: double score;
}

struct CategoryProfile {
    1: optional map<string, string> expose; // key: cate/subcate_1 2 3 4 5  value: cate/subcate
    2: optional map<string, string> clk;    // key: cate/subcate_1 2 3 4 5  value: cate/subcate
    3: optional i32 exposeCnt;
    4: optional i32 clickCnt;
}

struct ActionCounter {
  1: optional i32 expose; // 曝光
  2: optional i32 click;  // 点击
  3: optional i32 consume; // 消费：图文=浏览，视频=播放
  4: optional i32 valid_consume; // 有效消费：视频=播放时长>2s
  5: optional i32 duration;   // 时长：图文=浏览时长，视频=播放时长
  6: optional i32 like;   // 点赞
  7: optional i32 unlike; // 取消点赞
  8: optional i32 comment;    // 评论
  9: optional i32 share;  // 分享
  10: optional i32 over_play; // 完播：即视频播放进度达到100%
  11: optional double ctr; // ctr
  12: optional double over_rate; // 完播率 
  13: optional i32 expose_uv;  // 曝光 uv
  14: optional i32 click_uv; // 点击 uv
  15: optional double utr; // 点击uv/曝光uv
  16: optional i32 over_play_80; // 80%完播次数
  17: optional i32 hot_channel_card_expose; // 热点频道横滑插卡内容曝光
  18: optional i32 hot_channel_card_click; // 热点频道横滑插卡内容点击
  19: optional double hot_channel_card_ctr; // 热点频道横滑插卡内容ctr
  20: optional i32 rec_channel_card_expose;  // 推荐频道横滑插卡内容曝光
  21: optional i32 rec_channel_card_click;  // 推荐频道横滑插卡内容点击
  22: optional double rec_channel_card_ctr;  // 推荐频道横滑插卡内容ctr
  23: optional i32 i_list_expose; //列表页内容曝光
  24: optional i32 i_list_consume_dura;   // 内流第一页消费时长ms：图文=详情页浏览时长，视频=进入沉浸流第一个内容的播放时长
  25: optional double i_list_dtr; // 列表页曝光时长率
  26: optional double i_list_ctr; // 列表页点击率
  27: optional double play_percent_avg;   // 播放进度平均值 
  28: optional double play_percent_sum;   // 播放进度求和
  29: optional double play_percent_std;   // 播放进度标准差
  30: optional i32 dislike; // 不感兴趣次数
  31: optional i64 ack; // push 送达次数
  32: optional i32 enter_comment; // 进入评论
  33: optional i64 videoFastSlideCnt; // 视频快滑次数
  34: optional double videoFastSlideRate; // 视频快滑率
  35: optional double videoFastSlideWilsonRate; // wilson 修正的视频快滑率
}

struct ConsumeCounter {
  1: optional i32 consume_0_2s; // 消费时长(0-2s]消费次数
  2: optional i32 consume_2_5s; // 消费时长(2s-5s]消费次数
  3: optional i32 consume_5_10s; // 消费时长(5s,10s]消费次数
  4: optional i32 consume_10_30s; // 消费时长(10s,30s]消费次数
  5: optional i32 consume_30s; // 消费时长(30s+]消费次数
}

struct ActionCounters {
  1: map<string, ActionCounter> items;
  2: optional i64 updateTime;
  3: optional map<string, map<string, double>> multiItems; // 以 map 形式存储指标
  4: optional map<string, ConsumeCounter> seg_consume; // 消费分段统计
}

struct NormalCounter {
    1: optional double normalCtr; // 归一化 ctr
    2: optional double normal80Ctr; // 归一化 80 ctr
    3: optional double normalAvgCtr; // 归一化 avg ctr
    4: optional double normalOverRate; // 归一化完播率 
    5: optional double normalOver80Rate; // 归一化 80 完播率 
    6: optional double normalOverAvgRate; // 归一化 avg 完播率 
}

struct NormalStat {
    1: optional NormalCounter normal1d;
    2: optional NormalCounter normal3d;
    3: optional NormalCounter normal7d;
    4: optional NormalCounter normal30d;
    5: optional NormalCounter normal90d;
}

struct StatisticsProfile {
  1: map<string, ActionCounters> stats;
  2: optional NormalStat typeNormalStat;
  3: optional NormalStat cateNormalStat;
  4: optional NormalStat subcateNormalStat;
  5: optional NormalStat newcateNormalStat;
  6: optional NormalStat newsubcateNormalStat;
  7: optional NormalStat videoLengthSegNormalStat;
  8: optional NormalStat authorNormalStat;
}

struct Behavior {
  1: string id;
  2: i64 eventTime;
  3: optional i32 consumeDuration;
  4: optional i32 videoDuration;
  5: optional string cate1;
  6: optional string cate2;
  7: optional string originalId;
  8: optional list<string> keywords;
  9: optional string authorName;
  10: optional string ownerItemId; // 自有id
  11: optional string new_cate1;
  12: optional string new_cate2;
  13: optional string new_keywords;
  14: optional string introduOriginalAuthorName; // 原始作者
  15: optional string contentType; // 内容类型
  16: optional list<string> llmTextKeywords; // llmTextKeywords
  17: optional string channel; // 频道
  18: optional string page; // 页面
  19: optional string new_video_cate1; // v3版一级分类
  20: optional string new_video_cate2; // v3版二级分类
  21: optional string djy_region_info; // 地域信息
  22: optional list<string> llm_text_tags; // v3版关键词
  23: optional string newTextCategoryV2; // v4版一级分类
  24: optional string newTextSubCategoryV2; // v4版二级分类
}

struct PushAuthorBehavior {
  1: optional string authorId; // push 的作者id
  2: optional string authorName; // push 的作者名称
  3: optional i64 acks; // 送达次数
  4: optional i64 lastAckTs; // 最近一次送达时间戳
  5: optional i64 clicks; // 点击次数
  6: optional i64 lastClickTs; // 最近一次点击时间戳
  7: optional string authorCate; // 作者一级分类
  8: optional string authorSubcate; // 作者二级分类
  9: optional list<string> contentCate; // 内容一级分类
  10: optional list<string> contentSubcate; // 内容二级分类
  11: optional list<string> keywords; // 内容 keywords
  12: optional list<string> tags; // 内容 tags
  13: optional i32 authorLevel; // 作者等级
}

struct Behaviors {
  1: list<Behavior> items;
  2: optional i64 updateTime;
  3: optional list<PushAuthorBehavior> pushAuthorItems;
}

// tensor特征
struct Tensor {
  1: list<double> data; // 特征数据
  2: optional i32 dim; // 特征维度
  3: optional string name; // 特征名称
  4: optional string version; // 特征版本
  5: optional i64 updateTime;
}
                                                   
struct TensorProfile {
  1: optional map<string, Tensor> tensors; // 用户信息流画像-向量信息, key tensorname#version
}

struct UserBehaviorProfile {
    1: map<string, Behaviors> behaviors;
}

// 用户活跃画像
struct UserActiveProfile {
    1: map<string, i32> active_days;  // 活跃天数统计
}

// 用户向量
struct UserEmbedding {
    1: optional list<double> userTextEmbedding; // text 
    2: optional list<double> userIconEmbedding; // icon
    3: optional list<double> userDssmEmbedding; // dssm                                                 
}

struct UnionSearchRecord {
    1: optional i64 timestamp; // 搜索时间
    2: optional string search; // 搜索词
    3: optional list<string> tags; // 搜索词抽tag
    4: optional list<string> domain; // 领域
    5: optional list<string> category; // 一级类别
    6: optional list<string> keyword; // 关键词
    7: optional list<string> subcategory; // 二级类别
    8: optional string bizType; // 业务类型
}
                                       
struct InterestScore {
    1: optional map<string, double> categoryScores; // 一级类目兴趣偏好
    2: optional map<string, double> subcategoryScores; // 二级类目兴趣偏好
    3: optional map<string, double> tagScores; // tag 兴趣偏好
    4: optional map<string, double> keywordScores; // keyword 兴趣偏好
}

struct UnionSearchProfile {
    1: optional list<UnionSearchRecord> search; // 全域搜索词列表
    2: optional InterestScore globalInterestScore; // 全域兴趣分
    3: optional InterestScore searchInterestScore; // 搜索兴趣分
}

struct UserAlgoFeature {
  1: optional i32 clusterId;
}

// 用户完整画像
struct UserProfile {
  1: Common common; // common 画像

  2: StatisticsProfile user_feeds_stat_profile; // 用户信息流画像-统计信息
  3: UserBehaviorProfile user_feeds_seq_profile; // 用户信息流画像-序列信息
  4: UserActiveProfile user_feeds_active_profile; // 用户信息流画像-活跃信息

  5: StatisticsProfile user_feeds_realtime_stat_profile; // 用户信息流实时画像-实时统计信息
  6: UserBehaviorProfile user_feeds_realtime_seq_profile; // 用户信息流实时画像-实时序列信息

  7: StatisticsProfile user_push_stat_profile; // 用户Push画像-统计信息
  8: UserBehaviorProfile user_push_seq_profile; // 用户Push画像-序列信息
  9: UserActiveProfile user_push_active_profile; // 用户Push画像-活跃信息

  10: SearchStats search_stats; // 用户全搜场景

  11: CategoryProfile user_feeds_category_profile; // 用户信息流画像-类别信息
  12: CategoryProfile user_feeds_subcategory_profile; // 用户信息流画像-子类别信息
  13: CategoryProfile user_push_category_profile; // 用户Push画像-类别信息

  14: UserEmbedding userEmbedding; // 用户信息流画像-向量信息
                                                   
  15: TensorProfile userTensors; // 用户信息流画像-向量信息(新)
                                                   
  16: SearchStats search_stats_realtime; // 用户实时全搜场景
                                                   
  17: StatisticsProfile user_newhome_feeds_stat_profile; // 用户在内容中心的信息流画像-统计信息
                                                   
  18: StatisticsProfile user_xmpush_feeds_stat_profile; // 用户在竞品push的信息流画像-统计信息

  19: optional UnionSearchProfile unionSearchProfile; // 全域离线搜索画像

  20: optional UnionSearchProfile unionSearchProfileRealtime; // 全域实时搜索画像

  21: optional StatisticsProfile userPushAuthorStatProfile; // 用户 push 作者的画像-统计信息
  22: optional UserBehaviorProfile userPushAuthorSeqProfile; // 用户 push 作者的画像-序列信息

  23: optional UserAlgoFeature userAlgoFeature; // 用户算法特征
}