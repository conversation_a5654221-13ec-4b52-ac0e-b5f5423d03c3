[{"app_id": "application_123456_789012", "input": ["iceberg_zyjprc_hadoop.browser.dm_browser_user_feature_df"], "output": {"viewId": "U001", "viewName": "com.xiaomi.feeds.dujiangyan.userProfile", "version": "v1", "viewType": 1, "featureEntity": "User"}, "lineage": [{"field": [{"viewId": "U001", "viewName": "com.xiaomi.feeds.dujiangyan.userProfile", "viewType": 1, "path": "$|com.xiaomi.feeds.dujiangyan.user_feeds_stat_profile|stats['user_stat']"}], "dependencyColumns": [{"type": "iceberg", "table": "dm_browser_user_profile_feature_df", "catalog": "iceberg_zy<PERSON><PERSON><PERSON>_hadoop", "database": "browser", "column": "feed_cate_expose_1d"}, {"type": "iceberg", "table": "dm_browser_user_profile_feature_df", "catalog": "iceberg_zy<PERSON><PERSON><PERSON>_hadoop", "database": "browser", "column": "feed_cate_click_1d"}]}]}, {"app_id": "application_223344_556677", "input": ["iceberg_zyjprc_hadoop.browser.dm_browser_item_feature_df"], "output": {"viewId": "I001", "viewName": "com.xiaomi.feeds.dujiangyan.itemProfile", "version": "v1", "viewType": 2, "featureEntity": "<PERSON><PERSON>"}, "lineage": [{"field": [{"viewId": "I001", "viewName": "com.xiaomi.feeds.dujiangyan.itemProfile", "viewType": 2, "path": "$|com.xiaomi.feeds.dujiangyan.item_feeds_stat_profile|stats['item_stat']"}], "dependencyColumns": [{"type": "iceberg", "table": "dm_browser_item_feature_df", "catalog": "iceberg_zy<PERSON><PERSON><PERSON>_hadoop", "database": "browser", "column": "feature.item_category"}, {"type": "iceberg", "table": "dm_browser_item_feature_df", "catalog": "iceberg_zy<PERSON><PERSON><PERSON>_hadoop", "database": "browser", "column": "feature.item_price"}]}]}]