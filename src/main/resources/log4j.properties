# Root logger option
log4j.rootLogger=INFO, stdout, file

# Redirect log messages to console
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n

# Redirect log messages to a log file
log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.File=logs/application.log
log4j.appender.file.MaxFileSize=10MB
log4j.appender.file.MaxBackupIndex=10
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n

# Set specific logger levels to reduce noise
log4j.logger.org.apache.http=WARN
log4j.logger.org.apache.http.client.protocol.RequestAddCookies=WARN
log4j.logger.org.apache.http.wire=WARN
log4j.logger.org.apache.http.headers=WARN

# Set Spark related loggers to WARN to reduce verbosity
log4j.logger.org.apache.spark=WARN
log4j.logger.org.spark_project=WARN
log4j.logger.org.apache.hadoop=WARN

# Set other common noisy loggers to WARN
log4j.logger.org.eclipse.jetty=WARN
log4j.logger.io.netty=WARN
