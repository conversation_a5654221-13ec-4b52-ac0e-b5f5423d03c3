[{"viewId": "U001", "viewName": "com.xiaomi.feeds.dujiangyan.userProfile", "version": "v1", "viewType": 1, "featureEntity": "User"}, {"viewId": "I001", "viewName": "com.xiaomi.feeds.dujiangyan.itemProfile", "version": "v1", "viewType": 2, "featureEntity": "<PERSON><PERSON>"}, {"viewId": "Q001", "viewName": "com.xiaomi.feeds.dujiangyan.queryProfile", "version": "v1", "viewType": 3, "featureEntity": "Query"}, {"type": "iceberg", "table": "dm_browser_user_feature_df", "catalog": "iceberg_zj<PERSON><PERSON><PERSON>_hadoop", "database": "browser"}, {"type": "iceberg", "table": "dm_browser_item_feature_df", "catalog": "iceberg_zj<PERSON><PERSON><PERSON>_hadoop", "database": "browser"}, {"type": "iceberg", "table": "dm_browser_query_feature_df", "catalog": "iceberg_zj<PERSON><PERSON><PERSON>_hadoop", "database": "browser"}]