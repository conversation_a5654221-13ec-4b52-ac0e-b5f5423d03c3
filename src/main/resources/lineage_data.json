[
    {
        "field": {
            "viewId": "U001",
            "viewName": "com.xiaomi.feeds.dujiangyan.userProfile",
            "viewType": 1,
            "path": "$|com.xiaomi.feeds.dujiangyan.user_feeds_stat_profile|stats['user_stat']"
        },
        "dependencyColumns": [
            {
                "type": "iceberg",
                "table": "dm_browser_user_profile_feature_df",
                "catalog": "iceberg_zjyprc_hadoop",
                "database": "browser",
                "column": "feature.user_age"
            },
            {
                "type": "iceberg",
                "table": "dm_browser_user_profile_feature_df",
                "catalog": "iceberg_zjyprc_hadoop",
                "database": "browser",
                "column": "feature.user_gender"
            }
        ]
    }
]

[
    {
        "field": {
            "viewId": "I001",
            "viewName": "com.xiaomi.feeds.dujiangyan.itemProfile",
            "viewType": 2,
            "path": "$|com.xiaomi.feeds.dujiangyan.item_feeds_stat_profile|stats['item_stat']"
        },
        "dependencyColumns": [
            {
                "type": "iceberg",
                "table": "dm_browser_item_feature_df",
                "catalog": "iceberg_zjyprc_hadoop",
                "database": "browser",
                "column": "feature.item_category"
            },
            {
                "type": "iceberg",
                "table": "dm_browser_item_feature_df",
                "catalog": "iceberg_zjyprc_hadoop",
                "database": "browser",
                "column": "feature.item_price"
            }
        ]
    }
]

[
    {
        "field": {
            "viewId": "Q001",
            "viewName": "com.xiaomi.feeds.dujiangyan.queryProfile",
            "viewType": 3,
            "path": "$|com.xiaomi.feeds.dujiangyan.query_feeds_stat_profile|stats['query_stat']"
        },
        "dependencyColumns": [
            {
                "type": "iceberg",
                "table": "dm_browser_query_feature_df",
                "catalog": "iceberg_zjyprc_hadoop",
                "database": "browser",
                "column": "feature.query_text"
            },
            {
                "type": "iceberg",
                "table": "dm_browser_query_feature_df",
                "catalog": "iceberg_zjyprc_hadoop",
                "database": "browser",
                "column": "feature.query_length"
            }
        ]
    }
]
