package com.xiaomi.talosUtils;
/**
 * Copyright 2015, Xiaomi.
 * All rights reserved.
 * Author: <EMAIL>
 */

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicLong;

import libthrift091.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import org.apache.commons.io.IOUtils; // 需引入 commons-io 依赖

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.List;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;

import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
import com.xiaomi.infra.galaxy.talos.client.SimpleTopicAbnormalCallback;
import com.xiaomi.infra.galaxy.talos.producer.ProducerNotActiveException;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducer;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducerConfig;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageCallback;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageResult;
import com.xiaomi.infra.galaxy.talos.thrift.Message;

public class TalosProducerDemo {
    private static final Logger LOG = LoggerFactory.getLogger(TalosProducerDemo.class);

    // callback for producer success/fail to put message
    private static class MyMessageCallback implements UserMessageCallback {
        // count when success
        @Override
        public void onSuccess(UserMessageResult userMessageResult) {
            long count = successPutNumber.addAndGet(
                    userMessageResult.getMessageList().size());

            for (Message message : userMessageResult.getMessageList()) {
                LOG.info("success to put message: " + new String(message.getMessage()));
                
            }
            LOG.info("success to put message: " + count + " so far.");
        }

        // retry when failed
        @Override
        public void onError(UserMessageResult userMessageResult) {
            try {
                for (Message message : userMessageResult.getMessageList()) {
                    LOG.info("failed to put message: " + message + " we will retry to put it.");
                }
                talosProducer.addUserMessage(userMessageResult.getMessageList());
            } catch (ProducerNotActiveException e) {
                e.printStackTrace();
            }
        }
    }

    // you can init client config by put $your_propertyFile in your classpath
    // with the content of:
    /*
      galaxy.talos.service.endpoint=$talosServiceURI
    */
    private static final String propertyFileName = "$your_propertyFile";

    // 测试环境
    // private static final String accessKey = "AKICVUXZUXU3K2D4RN";
    // private static final String accessSecret = "qg9xHv+8RAaCI5wSw0elQFIEcd2UuDKR1R4k965P";
    // private static final String topicName = "view_extract_event_copy";

    // 正式环境
    private static final String accessKey = "AKOI316RCMCXIVOY2L";
    private static final String accessSecret = "DoDZlzmibTZf0jujWZEZhxPH1oNMOuOYYoY1Qoun";
    private static final String topicName = "view_extract_event";
    private static final int toPutMsgNumber = 1;
    private static final AtomicLong successPutNumber = new AtomicLong(0);

    private TalosProducerConfig producerConfig;
    private Credential credential;

    private static TalosProducer talosProducer;

    public TalosProducerDemo() {
        Properties pros = new Properties();
        // 测试环境
        // pros.setProperty("galaxy.talos.service.endpoint", "http://staging-cnbj2-talos.api.xiaomi.net");
        // 正式环境
        pros.setProperty("galaxy.talos.service.endpoint", "http://cnbj4-talos.api.xiaomi.net");
        producerConfig = new TalosProducerConfig(pros);

        // credential
        credential = new Credential();
        credential.setSecretKeyId(accessKey)
                .setSecretKey(accessSecret)
                .setType(UserType.DEV_XIAOMI);

    }

    public void start() throws TException {
        // init producer
        talosProducer = new TalosProducer(producerConfig, credential,
                topicName, new SimpleTopicAbnormalCallback(),
                new MyMessageCallback());

        ObjectMapper objectMapper = new ObjectMapper();
        List<Map<String, Object>> dataList = null;
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("sample_data.json")) {
            if (inputStream == null) {
                throw new RuntimeException("sample_data.json not found in classpath!");
            }
            String json = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            dataList = objectMapper.readValue(json, new TypeReference<List<Map<String, Object>>>() {});
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }
    
        List<Message> messageList = new ArrayList<>();
        // 用 Lambda 表达式遍历每条数据
        dataList.forEach(data -> {
            try {
                String messageStr = objectMapper.writeValueAsString(data);
                Message message = new Message(ByteBuffer.wrap(messageStr.getBytes()));
                messageList.add(message);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        });
        System.err.println("messageList: " + messageList);
        talosProducer.addUserMessage(messageList);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        // when call shutdown function,
        // the producer will wait all the messages in buffer to send to server

        //talosProducer.shutdown();
    }

    public static void main(String[] args) throws Exception {
        TalosProducerDemo producerDemo = new TalosProducerDemo();
        // add message list to producer continuously
        producerDemo.start();
    }
}