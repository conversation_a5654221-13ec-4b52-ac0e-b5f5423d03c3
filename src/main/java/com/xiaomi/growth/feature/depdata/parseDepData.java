package com.xiaomi.growth.feature.depdata;

import com.xiaomi.growth.feature.model.config.FeatureViewConfig;
import com.xiaomi.growth.feature.model.inapi.rsp.FeatureInfoRsp;
import com.xiaomi.growth.feature.yaml.CustomYamlParser;
import com.xiaomi.growth.feature.yaml.expression.FeatureReferenceExpression;
import com.xiaomi.growth.feature.yaml.expression.SqlBlockExpression;
import com.xiaomi.growth.feature.yaml.expression.SqlExpression;
import com.xiaomi.growth.feature.yaml.sqlgen.ParsedMappingConfig;
import com.xiaomi.growth.feature.yaml.sqlgen.SqlGeneratorNew;
import com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.ThriftTypeResolver;
import org.junit.Test;

import java.util.*;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.annotation.JsonProperty;
public class parseDepData {
    public static class dependency_data {
        @JsonProperty("app_id")
        public String app_id;

        @JsonProperty("input")
        public List<String> input;

        @JsonProperty("output")
        public Output output;

        @JsonProperty("lineage")
        public List<lineage_item> lineage;

        public dependency_data() {
            this.input = new ArrayList<>();
            this.output = new Output();
            this.app_id = "";
        }
    }

    public static class Output{
        @JsonProperty("viewId")
        public Long viewId;

        @JsonProperty("viewName")
        public String viewName;

        @JsonProperty("version")
        public String version;

        @JsonProperty("viewType")
        public Integer viewType;

        @JsonProperty("featureEntity")
        public String featureEntity;
    }

    public static class lineage_item {
        @JsonProperty("field")
        public List<String> field;

        @JsonProperty("dependencyColumns")
        public List<String> dependencyColumns;

        public lineage_item() {
            this.field = new ArrayList<>();
            this.dependencyColumns = new ArrayList<>();
        }
    }


    @Test
    public void testSqlGen() {
        dependency_data dd = new dependency_data();

        FeatureViewConfig config = CustomYamlParser.parseYamlFromResource("featview-test-baseinfo.yaml", FeatureViewConfig.class);

        dd.input = config.getBaseInfo().getDependencyTableName();
        dd.output.viewId = config.getBaseInfo().getViewId();
        dd.output.viewName = config.getBaseInfo().getViewName();
        dd.output.version = config.getBaseInfo().getVersion();
        dd.output.viewType = config.getBaseInfo().getViewType();
        dd.output.featureEntity = config.getBaseInfo().getFeatureEntity();

        FeatureViewConfig viewConfig = CustomYamlParser.parseYamlFromResource("feaview_u_djy_common_profile.yaml",
                FeatureViewConfig.class);

        List<lineage_item> lineage_list = new ArrayList<>();
        try {
            ParsedMappingConfig parsedMappingConfig = new ParsedMappingConfig(
                    viewConfig.getMappingConfig().getMapping(),
                    viewConfig.getSchema().getSerializeClassName(), new ThriftTypeResolver());


            Map<String, ParsedMappingConfig.Node> flattenMap = parsedMappingConfig.getFlattenMap();

            for (Map.Entry<String, ParsedMappingConfig.Node> entry : flattenMap.entrySet()) {
                lineage_item LI = new lineage_item();
                String featureName = entry.getKey();
                String path = entry.getValue().getPath();
                Object exp = entry.getValue().getValue();

                LI.field.add(path);
                if (exp instanceof FeatureReferenceExpression) {
                    FeatureReferenceExpression exp2 = (FeatureReferenceExpression) exp;
                    FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(exp2.getReference());

                    // Parse dependency column info
                    String fullTableName = fis.getFeatureStoreTable();
                    String column = fis.getFeatureStoreColumn();


                    LI.dependencyColumns.add(fullTableName + "." + column);

//                    System.out.println("FeatureName:" + featureName + " ," + "  Path:" + path + ",  Expression:"
//                            + exp2.getReference());
//                    System.out.println(
//                            "dependency_column: " + fis.getFeatureStoreTable() + "." + fis.getFeatureStoreColumn());
//                    System.out.println();

                    lineage_list.add(LI);

                } else if (exp instanceof SqlExpression) {
                    SqlExpression exp2 = (SqlExpression) exp;

                    exp2.extractFeatureRefs().stream().forEach(ref -> {
                        FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(ref);
                        String fullTableName = fis.getFeatureStoreTable();
                        String column = fis.getFeatureStoreColumn();
                        LI.dependencyColumns.add(fullTableName + "." + column);
                    });
                    lineage_list.add(LI);
//                    System.out.println("FeatureName:" + featureName + " ," + "  Path:" + path + ",  Expression:"
//                            + exp2.extractFeatureRefs());
//                    System.out.println();
                } else if (exp instanceof SqlBlockExpression) {
                    SqlBlockExpression exp2 = (SqlBlockExpression) exp;
                    exp2.getSqlExpressions().stream().forEach(sqlExp -> {
                        sqlExp.extractFeatureRefs().stream().forEach(ref -> {
                            FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(ref);
                            String fullTableName = fis.getFeatureStoreTable();
                            String column = fis.getFeatureStoreColumn();
                            LI.dependencyColumns.add(fullTableName + "." + column);
                        });
                    });

                    lineage_list.add(LI);
//                    System.out.println("FeatureName:" + featureName + " ," + "  Path:" + path + ",  Expression:"
//                            + exp2.getSqlExpressions().stream().map(SqlExpression::extractFeatureRefs)
//                                    .collect(Collectors.toList()));
//                    System.out.println();
                }
            }
            dd.lineage = lineage_list;

            // Output JSON
            ObjectMapper mapper = new ObjectMapper();
            String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(dd);
            System.out.println("\n=== JSON Output ===");
            System.out.println(jsonString);

            // System.out.println(SqlGeneratorNew.generateQueryStatement(parsedMappingConfig));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
