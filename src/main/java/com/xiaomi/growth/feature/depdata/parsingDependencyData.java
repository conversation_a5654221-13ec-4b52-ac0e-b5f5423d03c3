package com.xiaomi.growth.feature.parsing;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

public class parsingDependencyData {
    
    private final ObjectMapper objectMapper;
    private final String jsonFilePath;
    
    public parsingDependencyData() {
        this.objectMapper = new ObjectMapper();
        this.jsonFilePath = "src/main/resources/dependency_processed.json";
    }
    
    public parsingDependencyData(String customFilePath) {
        this.objectMapper = new ObjectMapper();
        this.jsonFilePath = customFilePath;
    }
    
    /**
     * 读取并解析JSON文件为DependencyRoot对象
     * @return 解析后的DependencyRoot对象
     * @throws IOException 文件读取错误
     */
    public DependencyRoot parseJsonFile() throws IOException {
        try {
            Path filePath = Paths.get(jsonFilePath);
            if (!Files.exists(filePath)) {
                throw new IOException("JSON文件不存在: " + jsonFilePath);
            }
            
            byte[] jsonBytes = Files.readAllBytes(filePath);
            String jsonContent = new String(jsonBytes, StandardCharsets.UTF_8);
            return objectMapper.readValue(jsonContent, DependencyRoot.class);
        } catch (JsonProcessingException e) {
            throw new IOException("JSON解析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 读取JSON文件为JsonNode对象，提供更灵活的访问方式
     * @return JsonNode对象
     * @throws IOException 文件读取错误
     */
    public JsonNode parseJsonToNode() throws IOException {
        try {
            Path filePath = Paths.get(jsonFilePath);
            if (!Files.exists(filePath)) {
                throw new IOException("JSON文件不存在: " + jsonFilePath);
            }
            
            byte[] jsonBytes = Files.readAllBytes(filePath);
            String jsonContent = new String(jsonBytes, StandardCharsets.UTF_8);
            return objectMapper.readTree(jsonContent);
        } catch (JsonProcessingException e) {
            throw new IOException("JSON解析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取所有components列表
     * @return 所有components的列表
     * @throws IOException 文件读取错误
     */
    public List<String> getAllComponents() throws IOException {
        List<String> allComponents = new ArrayList<>();
        DependencyRoot root = parseJsonFile();
        
        if (root.getLineage() != null && root.getLineage().getFields() != null) {
            // 使用Lambda表达式遍历fields
            root.getLineage().getFields().stream()
                .forEach(field -> collectComponentsFromField(field, allComponents));
        }
        
        return allComponents;
    }
    
    /**
     * 递归收集field中的所有components
     */
    private void collectComponentsFromField(FieldData field, List<String> components) {
        if (field.getComponents() != null) {
            components.addAll(field.getComponents());
        }
        
        if (field.getFields() != null) {
            field.getFields().stream()
                .forEach(subField -> collectComponentsFromField(subField, components));
        }
    }
    
    /**
     * 根据路径查找特定字段
     * @param path 字段路径
     * @return 匹配的字段（如果存在）
     * @throws IOException 文件读取错误
     */
    public Optional<FieldData> findFieldByPath(String path) throws IOException {
        DependencyRoot root = parseJsonFile();
        if (root.getLineage() != null && root.getLineage().getFields() != null) {
            return findFieldByPathRecursive(root.getLineage().getFields(), path);
        }
        return Optional.empty();
    }
    
    private Optional<FieldData> findFieldByPathRecursive(List<FieldData> fields, String targetPath) {
        // 先查找直接匹配的字段
        Optional<FieldData> directMatch = fields.stream()
            .filter(field -> targetPath.equals(field.getPath()))
            .findFirst();
        
        if (directMatch.isPresent()) {
            return directMatch;
        }
        
        // 如果没有直接匹配，递归查找子字段
        for (FieldData field : fields) {
            if (field.getFields() != null) {
                Optional<FieldData> subMatch = findFieldByPathRecursive(field.getFields(), targetPath);
                if (subMatch.isPresent()) {
                    return subMatch;
                }
            }
        }
        
        return Optional.empty();
    }
    
    /**
     * 获取指定类型的所有字段
     * @param type 字段类型
     * @return 匹配类型的字段列表
     * @throws IOException 文件读取错误
     */
    public List<FieldData> getFieldsByType(String type) throws IOException {
        List<FieldData> result = new ArrayList<>();
        DependencyRoot root = parseJsonFile();
        
        if (root.getLineage() != null && root.getLineage().getFields() != null) {
            root.getLineage().getFields().stream()
                .forEach(field -> collectFieldsByType(field, type, result));
        }
        
        return result;
    }
    
    private void collectFieldsByType(FieldData field, String targetType, List<FieldData> result) {
        if (targetType.equals(field.getType())) {
            result.add(field);
        }
        
        if (field.getFields() != null) {
            field.getFields().stream()
                .forEach(subField -> collectFieldsByType(subField, targetType, result));
        }
    }
    
    /**
     * 获取components不为空的所有节点
     * @return components不为空的字段列表
     * @throws IOException 文件读取错误
     */
    public List<FieldData> getNodesWithNonEmptyComponents() throws IOException {
        List<FieldData> result = new ArrayList<>();
        DependencyRoot root = parseJsonFile();
        
        if (root.getLineage() != null && root.getLineage().getFields() != null) {
            root.getLineage().getFields().stream()
                .forEach(field -> collectNodesWithNonEmptyComponents(field, result));
        }
        
        return result;
    }
    
    /**
     * 递归收集components不为空的节点
     * @param field 当前字段
     * @param result 结果列表
     */
    private void collectNodesWithNonEmptyComponents(FieldData field, List<FieldData> result) {
        // 检查当前节点的components是否不为空
        if (field.getComponents() != null && !field.getComponents().isEmpty()) {
            result.add(field);
        }
        
        // 递归检查子字段
        if (field.getFields() != null) {
            field.getFields().stream()
                .forEach(subField -> collectNodesWithNonEmptyComponents(subField, result));
        }
    }
    
    /**
     * 获取包含指定component的所有节点
     * @param componentName 要查找的component名称
     * @return 包含指定component的字段列表
     * @throws IOException 文件读取错误
     */
    public List<FieldData> getNodesByComponent(String componentName) throws IOException {
        List<FieldData> result = new ArrayList<>();
        DependencyRoot root = parseJsonFile();
        
        if (root.getLineage() != null && root.getLineage().getFields() != null) {
            root.getLineage().getFields().stream()
                .forEach(field -> collectNodesByComponent(field, componentName, result));
        }
        
        return result;
    }
    
    /**
     * 递归收集包含指定component的节点
     * @param field 当前字段
     * @param componentName 要查找的component名称
     * @param result 结果列表
     */
    private void collectNodesByComponent(FieldData field, String componentName, List<FieldData> result) {
        // 检查当前节点是否包含指定的component
        if (field.getComponents() != null && field.getComponents().contains(componentName)) {
            result.add(field);
        }
        
        // 递归检查子字段
        if (field.getFields() != null) {
            field.getFields().stream()
                .forEach(subField -> collectNodesByComponent(subField, componentName, result));
        }
    }
    
    /**
     * 获取应用ID
     * @return 应用ID
     * @throws IOException 文件读取错误
     */
    public String getAppId() throws IOException {
        DependencyRoot root = parseJsonFile();
        return root.getAppId();
    }
    
    /**
     * 获取输入数据源列表
     * @return 输入数据源列表
     * @throws IOException 文件读取错误
     */
    public List<String> getInputSources() throws IOException {
        DependencyRoot root = parseJsonFile();
        return root.getInput();
    }
    
    /**
     * 获取输出配置信息
     * @return 输出配置对象
     * @throws IOException 文件读取错误
     */
    public OutputConfig getOutputConfig() throws IOException {
        DependencyRoot root = parseJsonFile();
        return root.getOutput();
    }
    
    /**
     * 获取血缘关系数据
     * @return 血缘关系对象
     * @throws IOException 文件读取错误
     */
    public LineageData getLineageData() throws IOException {
        DependencyRoot root = parseJsonFile();
        return root.getLineage();
    }
    
    // 数据模型类
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DependencyRoot {
        @JsonProperty("app_id")
        private String appId;
        private List<String> input;
        private OutputConfig output;
        private LineageData lineage;
        
        // Getters and Setters
        public String getAppId() { return appId; }
        public void setAppId(String appId) { this.appId = appId; }
        
        public List<String> getInput() { return input; }
        public void setInput(List<String> input) { this.input = input; }
        
        public OutputConfig getOutput() { return output; }
        public void setOutput(OutputConfig output) { this.output = output; }
        
        public LineageData getLineage() { return lineage; }
        public void setLineage(LineageData lineage) { this.lineage = lineage; }
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OutputConfig {
        private String viewId;
        private String viewName;
        private String version;
        private Integer viewType;
        private String featureEntity;
        
        // Getters and Setters
        public String getViewId() { return viewId; }
        public void setViewId(String viewId) { this.viewId = viewId; }
        
        public String getViewName() { return viewName; }
        public void setViewName(String viewName) { this.viewName = viewName; }
        
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
        
        public Integer getViewType() { return viewType; }
        public void setViewType(Integer viewType) { this.viewType = viewType; }
        
        public String getFeatureEntity() { return featureEntity; }
        public void setFeatureEntity(String featureEntity) { this.featureEntity = featureEntity; }
        
        @Override
        public String toString() {
            return String.format("OutputConfig{viewId='%s', viewName='%s', version='%s', viewType=%d, featureEntity='%s'}", 
                viewId, viewName, version, viewType, featureEntity);
        }
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LineageData {
        private String name;
        private String type;
        private String path;
        private List<FieldData> fields;
        private List<String> components;
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
        
        public List<FieldData> getFields() { return fields; }
        public void setFields(List<FieldData> fields) { this.fields = fields; }
        
        public List<String> getComponents() { return components; }
        public void setComponents(List<String> components) { this.components = components; }
        
        @Override
        public String toString() {
            return String.format("LineageData{name='%s', type='%s', path='%s', fieldsCount=%d}", 
                name, type, path, fields != null ? fields.size() : 0);
        }
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FieldData {
        private String name;
        private String type;
        private String path;
        private List<FieldData> fields;
        private List<String> components;
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
        
        public List<FieldData> getFields() { return fields; }
        public void setFields(List<FieldData> fields) { this.fields = fields; }
        
        public List<String> getComponents() { return components; }
        public void setComponents(List<String> components) { this.components = components; }
        
        @Override
        public String toString() {
            return String.format("FieldData{name='%s', type='%s', path='%s'}", name, type, path);
        }
    }
    
    // 示例使用方法
    public static void main(String[] args) {
        parsingDependencyData parser = new parsingDependencyData();
        
        try {
            // 方法1: 读取基本信息
            System.out.println("=== 基本信息 ===");
            String appId = parser.getAppId();
            System.out.println("应用ID: " + appId);
            
            List<String> inputSources = parser.getInputSources();
            System.out.println("输入源数量: " + inputSources.size());
            inputSources.stream().forEach(source -> System.out.println("- " + source));
            
            OutputConfig output = parser.getOutputConfig();
            System.out.println("输出配置: " + output.toString());
            
            // 方法2: 读取血缘关系信息
            System.out.println("\n=== 血缘关系信息 ===");
            LineageData lineage = parser.getLineageData();
            System.out.println("血缘数据: " + lineage.toString());
            
            // 方法3: 解析为完整对象
            System.out.println("\n=== 完整解析 ===");
            DependencyRoot root = parser.parseJsonFile();
            System.out.println("应用ID: " + root.getAppId());
            System.out.println("血缘名称: " + root.getLineage().getName());
            System.out.println("血缘类型: " + root.getLineage().getType());
            System.out.println("字段数量: " + (root.getLineage().getFields() != null ? root.getLineage().getFields().size() : 0));
            
            // 方法4: 获取所有components
            System.out.println("\n=== 获取所有Components ===");
            List<String> allComponents = parser.getAllComponents();
            System.out.printf("总共找到 %d 个components%n", allComponents.size());
            allComponents.stream()
                .limit(5) // 只显示前5个
                .forEach(component -> System.out.println("- " + component));
            
            // 方法5: 根据路径查找字段
            System.out.println("\n=== 根据路径查找字段 ===");
            Optional<FieldData> field = parser.findFieldByPath("$.common.age");
            if (field.isPresent()) {
                System.out.println("找到字段: " + field.get().toString());
            } else {
                System.out.println("未找到指定路径的字段");
            }
            
            // 方法6: 根据类型查找字段
            System.out.println("\n=== 根据类型查找字段 ===");
            List<FieldData> doubleFields = parser.getFieldsByType("double");
            System.out.printf("找到 %d 个double类型字段%n", doubleFields.size());
            doubleFields.stream()
                .limit(3)
                .forEach(f -> System.out.println("- " + f.getName() + " (" + f.getPath() + ")"));
            
            // 方法7: 获取components不为空的节点
            System.out.println("\n=== 获取components不为空的节点 ===");
            List<FieldData> nodesWithComponents = parser.getNodesWithNonEmptyComponents();
            System.out.printf("找到 %d 个包含components的节点%n", nodesWithComponents.size());
            nodesWithComponents.stream()
                .limit(5) // 只显示前5个
                .forEach(node -> {
                    System.out.printf("节点: %s (路径: %s, components数量: %d)%n", 
                        node.getName(), node.getPath(), 
                        node.getComponents() != null ? node.getComponents().size() : 0);
                    if (node.getComponents() != null && !node.getComponents().isEmpty()) {
                        System.out.println("  Components: " + String.join(", ", node.getComponents()));
                    }
                });
            
            // 方法8: 根据特定component查找节点
            System.out.println("\n=== 根据特定component查找节点 ===");
            if (!allComponents.isEmpty()) {
                String firstComponent = allComponents.get(0);
                List<FieldData> nodesByComponent = parser.getNodesByComponent(firstComponent);
                System.out.printf("包含component '%s' 的节点数量: %d%n", firstComponent, nodesByComponent.size());
                nodesByComponent.stream()
                    .limit(3)
                    .forEach(node -> System.out.println("- " + node.getName() + " (" + node.getPath() + ")"));
            }
            
            // 方法9: 使用JsonNode灵活访问
            System.out.println("\n=== JsonNode灵活访问 ===");
            JsonNode rootNode = parser.parseJsonToNode();
            System.out.println("应用ID: " + rootNode.get("app_id").asText());
            System.out.println("视图ID: " + rootNode.get("output").get("viewId").asText());
            System.out.println("血缘名称: " + rootNode.get("lineage").get("name").asText());
            
        } catch (IOException e) {
            System.err.println("处理JSON文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
