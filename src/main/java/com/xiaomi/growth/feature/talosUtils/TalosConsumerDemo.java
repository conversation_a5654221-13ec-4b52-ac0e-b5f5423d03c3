package com.xiaomi.growth.feature.talosUtils; /**
 * Copyright 2015, Xiao<PERSON>.
 * All rights reserved.
 * Author: <EMAIL>
 */

 import java.util.List;
 
 import java.util.Properties;
 import java.util.concurrent.atomic.AtomicLong;
 
 import libthrift091.TException;
 import org.slf4j.Logger;
 import org.slf4j.LoggerFactory;
 
 import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
 import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
 import com.xiaomi.infra.galaxy.talos.client.SimpleTopicAbnormalCallback;
 import com.xiaomi.infra.galaxy.talos.consumer.MessageCheckpointer;
 import com.xiaomi.infra.galaxy.talos.consumer.MessageProcessor;
 import com.xiaomi.infra.galaxy.talos.consumer.MessageProcessorFactory;
 import com.xiaomi.infra.galaxy.talos.consumer.TalosConsumer;
 import com.xiaomi.infra.galaxy.talos.consumer.TalosConsumerConfig;
 import com.xiaomi.infra.galaxy.talos.thrift.MessageAndOffset;
 import com.xiaomi.infra.galaxy.talos.thrift.TopicAndPartition;



 public class TalosConsumerDemo {
   private static final Logger LOG = LoggerFactory.getLogger(TalosConsumerDemo.class);
 
   // callback for consumer to process messages, that is, consuming logic
   private static class MyMessageProcessor implements MessageProcessor {
     @Override
     public void init(TopicAndPartition topicAndPartition, long messageOffset) {
 
     }
 
     @Override
     public void process(List<MessageAndOffset> messages, MessageCheckpointer messageCheckpointer) {
       try {
         // add your process logic for 'messages'
         for (MessageAndOffset messageAndOffset : messages) {
           LOG.info("Message content: " + new String(
               messageAndOffset.getMessage().getMessage()));
          System.out.println("Message content: " + new String(
          messageAndOffset.getMessage().getMessage()));
         }
 
         long count = successGetNumber.addAndGet(messages.size());
         LOG.info("Consuming total data so far: " + count);
 
         /** if user has set 'galaxy.talos.consumer.checkpoint.auto.commit' to false,
          * then you can call the 'checkpoint' to commit the list of messages.
          */
         //messageCheckpointer.checkpoint();
       } catch (Throwable throwable) {
         LOG.error("process error, ", throwable);
       }
     }
 
     @Override
     public void shutdown(MessageCheckpointer messageCheckpointer) {
 
     }
   }
 
   // using for thread-safe when processing different partition data
   private static class MyMessageProcessorFactory implements MessageProcessorFactory {
     @Override
     public MessageProcessor createProcessor() {
       return new MyMessageProcessor();
     }
   }
 
   // you can init client config by put $your_propertyFile in your classpath
   // with the content of:
   /*
     galaxy.talos.service.endpoint=$talosServiceURI
  //  */

  // 正式环境
   private static final String accessKey = "AKOI316RCMCXIVOY2L";
   private static final String accessSecret = "DoDZlzmibTZf0jujWZEZhxPH1oNMOuOYYoY1Qoun";
   private static final String topicName = "view_extract_event";

  // // 测试环境
  // private static final String accessKey = "AKICVUXZUXU3K2D4RN";
  // private static final String accessSecret = "qg9xHv+8RAaCI5wSw0elQFIEcd2UuDKR1R4k965P";
  // private static final String topicName = "view_extract_event_copy";
   private static final AtomicLong successGetNumber = new AtomicLong(0);

   private static final String clientPrefix = "viewExtractConsumer-";
   private static final String consumerGroup = "viewExtractGroup";
 
   private TalosConsumerConfig consumerConfig;
   private Credential credential;
   private TalosConsumer talosConsumer;
 
   public TalosConsumerDemo() {
        Properties pros = new Properties();
        // 正式环境
        pros.setProperty("galaxy.talos.service.endpoint", "http://cnbj4-talos.api.xiaomi.net");
        // 测试环境
        // pros.setProperty("galaxy.talos.service.endpoint", "http://staging-cnbj2-talos.api.xiaomi.net");
        consumerConfig = new TalosConsumerConfig(pros);

        // credential
        credential = new Credential();
        credential.setSecretKeyId(accessKey)
            .setSecretKey(accessSecret)
            .setType(UserType.DEV_XIAOMI);
    }
 
   public void start() throws TException {
     talosConsumer = new TalosConsumer(consumerGroup, consumerConfig,
         credential, topicName, new MyMessageProcessorFactory(),
         clientPrefix, new SimpleTopicAbnormalCallback());
   }
 
   public static void main(String[] args) throws Exception {
     TalosConsumerDemo consumerDemo = new TalosConsumerDemo();
     consumerDemo.start();
   }
 }