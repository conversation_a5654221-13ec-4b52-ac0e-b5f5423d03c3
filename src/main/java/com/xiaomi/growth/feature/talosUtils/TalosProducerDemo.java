package com.xiaomi.growth.feature.talosUtils;
/**
 * Copyright 2015, Xiao<PERSON>.
 * All rights reserved.
 * Author: <EMAIL>
 */

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicLong;

import com.xiaomi.growth.feature.depdata.parseDepData;
import libthrift091.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;


import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
import com.xiaomi.infra.galaxy.talos.client.SimpleTopicAbnormalCallback;
import com.xiaomi.infra.galaxy.talos.producer.ProducerNotActiveException;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducer;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducerConfig;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageCallback;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageResult;
import com.xiaomi.infra.galaxy.talos.thrift.Message;

public class TalosProducerDemo {
    private static final Logger LOG = LoggerFactory.getLogger(TalosProducerDemo.class);

    // callback for producer success/fail to put message
    private static class MyMessageCallback implements UserMessageCallback {
        // count when success
        @Override
        public void onSuccess(UserMessageResult userMessageResult) {
            long count = successPutNumber.addAndGet(
                    userMessageResult.getMessageList().size());

            for (Message message : userMessageResult.getMessageList()) {
                LOG.info("success to put message: " + new String(message.getMessage()));
                
            }
            LOG.info("success to put message: " + count + " so far.");
        }

        // retry when failed
        @Override
        public void onError(UserMessageResult userMessageResult) {
            try {
                for (Message message : userMessageResult.getMessageList()) {
                    LOG.info("failed to put message: " + message + " we will retry to put it.");
                }
                talosProducer.addUserMessage(userMessageResult.getMessageList());
            } catch (ProducerNotActiveException e) {
                e.printStackTrace();
            }
        }
    }

    private static final String propertyFileName = "$your_propertyFile";



    // 正式环境
    private static final String accessKey = "AKOI316RCMCXIVOY2L";
    private static final String accessSecret = "DoDZlzmibTZf0jujWZEZhxPH1oNMOuOYYoY1Qoun";
    private static final String topicName = "view_extract_event";
    private static final int toPutMsgNumber = 1;
    private static final AtomicLong successPutNumber = new AtomicLong(0);

    private TalosProducerConfig producerConfig;
    private Credential credential;

    private static TalosProducer talosProducer;

    public TalosProducerDemo() {
        Properties pros = new Properties();

        // 正式环境
        pros.setProperty("galaxy.talos.service.endpoint", "http://cnbj4-talos.api.xiaomi.net");
        producerConfig = new TalosProducerConfig(pros);

        // credential
        credential = new Credential();
        credential.setSecretKeyId(accessKey)
                .setSecretKey(accessSecret)
                .setType(UserType.DEV_XIAOMI);

    }

    public void start() throws TException, InterruptedException {
        // init producer
        talosProducer = new TalosProducer(producerConfig, credential,
                topicName, new SimpleTopicAbnormalCallback(),
                new MyMessageCallback());

        // 使用parseDepData生成JSON数据
        parseDepData parser = new parseDepData();
        String jsonString = parser.generateDependencyJson(
                "featview-test-baseinfo.yaml",
                "feaview_u_djy_common_profile.yaml"
        );

        // 创建消息并发送到Talos
        Message message = new Message(ByteBuffer.wrap(jsonString.getBytes(StandardCharsets.UTF_8)));
        List<Message> messageList = new ArrayList<>();
        messageList.add(message);

        talosProducer.addUserMessage(messageList);

        Thread.sleep(10000);
    }

    public void sendDependencyData(String baseInfoYaml, String extractYaml) throws TException {
        // init producer
        talosProducer = new TalosProducer(producerConfig, credential,
                topicName, new SimpleTopicAbnormalCallback(),
                new MyMessageCallback());

        try {
            parseDepData parser = new parseDepData();
            String jsonString = parser.generateDependencyJson(baseInfoYaml, extractYaml);

            Message message = new Message(ByteBuffer.wrap(jsonString.getBytes(StandardCharsets.UTF_8)));
            List<Message> messageList = new ArrayList<>();
            messageList.add(message);


            LOG.info("发送依赖数据JSON到Talos: {}", jsonString);
            talosProducer.addUserMessage(messageList);

            Thread.sleep(5000);
        } catch (Exception e) {
            LOG.error("发送数据到Talos失败", e);
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws Exception {
        TalosProducerDemo producerDemo = new TalosProducerDemo();
        producerDemo.sendDependencyData("featview-test-baseinfo.yaml", "feaview_u_djy_common_profile.yaml");
    }
}