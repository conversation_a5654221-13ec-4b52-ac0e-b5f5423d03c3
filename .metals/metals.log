2025.06.27 14:19:26 INFO  Started: Metals version 1.6.0 in folders 'D:\code\feature-metasdk' for client Cursor 1.96.2.
2025.06.27 14:19:27 INFO  no build target found for D:\code\feature-metasdk\src\main\scala\com\xiaomi\hdfsUtils\hdfsReader.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.27 14:19:29 INFO  running 'C:\Users\<USER>\AppData\Local\Coursier\cache\arc\https\github.com\adoptium\temurin17-binaries\releases\download\jdk-17.0.14%252B7\OpenJDK17U-jdk_x64_windows_hotspot_17.0.14_7.zip\jdk-17.0.14+7\bin\java.exe -Dfile.encoding=UTF-8 -Dmaven.multiModuleProjectDirectory=D:\code\feature-metasdk -Dmaven.home=C:\Users\<USER>\AppData\Local\Temp\metals1390166064364968869 -cp C:\Users\<USER>\AppData\Local\Temp\metals1390166064364968869\maven-wrapper.jar org.apache.maven.wrapper.MavenWrapperMain generate-sources ch.epfl.scala:bloop-maven-plugin:2.0.1:bloopInstall -DdownloadSources=true'
2025.06.27 14:19:30 INFO  [INFO] Scanning for projects...
2025.06.27 14:19:30 INFO  [WARNING] 
2025.06.27 14:19:30 INFO  [WARNING] Some problems were encountered while building the effective model for org.example:dependencyProducer:jar:1.0-SNAPSHOT
2025.06.27 14:19:30 INFO  [WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.apache.thrift:libthrift:jar -> version 0.14.2 vs 0.15.0 @ line 38, column 17
2025.06.27 14:19:30 INFO  [WARNING] 
2025.06.27 14:19:30 INFO  [WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
2025.06.27 14:19:30 INFO  [WARNING] 
2025.06.27 14:19:30 INFO  [WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
2025.06.27 14:19:30 INFO  [WARNING] 
2025.06.27 14:19:30 INFO  [INFO] 
2025.06.27 14:19:30 INFO  [INFO] -------------------< org.example:dependencyProducer >-------------------
2025.06.27 14:19:30 INFO  [INFO] Building Archetype - dependencyProducer 1.0-SNAPSHOT
2025.06.27 14:19:30 INFO  [INFO]   from pom.xml
2025.06.27 14:19:30 INFO  [INFO] --------------------------------[ jar ]---------------------------------
2025.06.27 14:19:30 INFO  [INFO] 
2025.06.27 14:19:30 INFO  [INFO] --- bloop:2.0.1:bloopInstall (default-cli) @ dependencyProducer ---
2025.06.27 14:19:32 INFO  [ERROR] org.scala-lang:scala([0-9]?)-library is missing from project dependencies
2025.06.27 14:19:32 INFO  [ERROR] org.scala-lang:scala([0-9]?)-library is missing from project dependencies
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.twitter.common:stats:jar:0.0.89:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.twitter.common:stats:jar:0.0.89:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.twitter.common:stats:jar:0.0.89:compile (sources = true)
2025.06.27 14:19:32 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/stats/0.0.89/stats-0.0.89-sources.jar
2025.06.27 14:19:32 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/stats/0.0.89/stats-0.0.89-sources.jar
2025.06.27 14:19:32 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/stats/0.0.89/stats-0.0.89-sources.jar
2025.06.27 14:19:32 INFO  Progress (1): 7.4/21 kB
2025.06.27 14:19:32 INFO  Progress (1): 16/21 kB 
2025.06.27 14:19:32 INFO  Progress (1): 21 kB   
2025.06.27 14:19:32 INFO                     
2025.06.27 14:19:32 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/stats/0.0.89/stats-0.0.89-sources.jar (21 kB at 564 kB/s)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.twitter.common:stats:jar:0.0.89:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: org.apache.thrift:libthrift:jar:0.15.0:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: org.apache.thrift:libthrift:jar:0.15.0:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: org.apache.thrift:libthrift:jar:0.15.0:compile (sources = true)
2025.06.27 14:19:32 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/thrift/libthrift/0.15.0/libthrift-0.15.0-sources.jar
2025.06.27 14:19:32 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/thrift/libthrift/0.15.0/libthrift-0.15.0-sources.jar
2025.06.27 14:19:32 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/thrift/libthrift/0.15.0/libthrift-0.15.0-sources.jar
2025.06.27 14:19:32 INFO  Progress (1): 7.4/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 16/219 kB 
2025.06.27 14:19:32 INFO  Progress (1): 32/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 48/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 65/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 81/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 97/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 114/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 130/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 147/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 163/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 179/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 196/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 212/219 kB
2025.06.27 14:19:32 INFO  Progress (1): 219 kB    
2025.06.27 14:19:32 INFO                      
2025.06.27 14:19:32 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/thrift/libthrift/0.15.0/libthrift-0.15.0-sources.jar (219 kB at 1.3 MB/s)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: org.apache.thrift:libthrift:jar:0.15.0:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile (sources = true)
2025.06.27 14:19:32 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/googlecode/concurrentlinkedhashmap/concurrentlinkedhashmap-lru/1.4.2/concurrentlinkedhashmap-lru-1.4.2-sources.jar
2025.06.27 14:19:32 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/googlecode/concurrentlinkedhashmap/concurrentlinkedhashmap-lru/1.4.2/concurrentlinkedhashmap-lru-1.4.2-sources.jar
2025.06.27 14:19:32 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/googlecode/concurrentlinkedhashmap/concurrentlinkedhashmap-lru/1.4.2/concurrentlinkedhashmap-lru-1.4.2-sources.jar
2025.06.27 14:19:32 INFO  Progress (1): 7.3/57 kB
2025.06.27 14:19:32 INFO  Progress (1): 16/57 kB 
2025.06.27 14:19:32 INFO  Progress (1): 32/57 kB
2025.06.27 14:19:32 INFO  Progress (1): 48/57 kB
2025.06.27 14:19:32 INFO  Progress (1): 57 kB   
2025.06.27 14:19:32 INFO                     
2025.06.27 14:19:32 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/googlecode/concurrentlinkedhashmap/concurrentlinkedhashmap-lru/1.4.2/concurrentlinkedhashmap-lru-1.4.2-sources.jar (57 kB at 2.1 MB/s)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.google.protobuf:protobuf-java:jar:2.5.0:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.google.protobuf:protobuf-java:jar:2.5.0:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.google.protobuf:protobuf-java:jar:2.5.0:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.google.protobuf:protobuf-java:jar:2.5.0:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.twitter.common:stats-provider:jar:0.0.53:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.twitter.common:stats-provider:jar:0.0.53:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.twitter.common:stats-provider:jar:0.0.53:compile (sources = true)
2025.06.27 14:19:32 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/stats-provider/0.0.53/stats-provider-0.0.53-sources.jar
2025.06.27 14:19:32 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/stats-provider/0.0.53/stats-provider-0.0.53-sources.jar
2025.06.27 14:19:32 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/stats-provider/0.0.53/stats-provider-0.0.53-sources.jar
2025.06.27 14:19:32 INFO  Progress (1): 1.7 kB
2025.06.27 14:19:32 INFO                      
2025.06.27 14:19:32 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/stats-provider/0.0.53/stats-provider-0.0.53-sources.jar (1.7 kB at 64 kB/s)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.twitter.common:stats-provider:jar:0.0.53:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.twitter.common:stat:jar:0.0.26:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.twitter.common:stat:jar:0.0.26:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: com.twitter.common:stat:jar:0.0.26:compile (sources = true)
2025.06.27 14:19:32 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/stat/0.0.26/stat-0.0.26-sources.jar
2025.06.27 14:19:32 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/stat/0.0.26/stat-0.0.26-sources.jar
2025.06.27 14:19:32 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/stat/0.0.26/stat-0.0.26-sources.jar
2025.06.27 14:19:32 INFO  Progress (1): 1.2 kB
2025.06.27 14:19:32 INFO                      
2025.06.27 14:19:32 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/stat/0.0.26/stat-0.0.26-sources.jar (1.2 kB at 46 kB/s)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: com.twitter.common:stat:jar:0.0.26:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: aopalliance:aopalliance:jar:1.0:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: aopalliance:aopalliance:jar:1.0:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: aopalliance:aopalliance:jar:1.0:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: aopalliance:aopalliance:jar:1.0:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: org.lz4:lz4-java:jar:1.8.0:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: org.lz4:lz4-java:jar:1.8.0:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: org.lz4:lz4-java:jar:1.8.0:compile (sources = true)
2025.06.27 14:19:32 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0-sources.jar
2025.06.27 14:19:32 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0-sources.jar
2025.06.27 14:19:32 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0-sources.jar
2025.06.27 14:19:32 INFO  Progress (1): 7.4/68 kB
2025.06.27 14:19:32 INFO  Progress (1): 16/68 kB 
2025.06.27 14:19:32 INFO  Progress (1): 32/68 kB
2025.06.27 14:19:32 INFO  Progress (1): 48/68 kB
2025.06.27 14:19:32 INFO  Progress (1): 65/68 kB
2025.06.27 14:19:32 INFO  Progress (1): 68 kB   
2025.06.27 14:19:32 INFO                     
2025.06.27 14:19:32 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0-sources.jar (68 kB at 2.2 MB/s)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: org.lz4:lz4-java:jar:1.8.0:compile (sources = true)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-simple:jar:1.7.36:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] SUCCESS: org.slf4j:slf4j-simple:jar:1.7.36:compile (sources = false)
2025.06.27 14:19:32 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-simple:jar:1.7.36:compile (sources = true)
2025.06.27 14:19:32 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36-sources.jar
2025.06.27 14:19:32 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36-sources.jar
2025.06.27 14:19:33 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 7.3/15 kB
2025.06.27 14:19:33 INFO  Progress (1): 15 kB    
2025.06.27 14:19:33 INFO                     
2025.06.27 14:19:33 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36-sources.jar (15 kB at 588 kB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.slf4j:slf4j-simple:jar:1.7.36:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.apache.commons:commons-compress:jar:1.8.1:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.apache.commons:commons-compress:jar:1.8.1:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.apache.commons:commons-compress:jar:1.8.1:compile (sources = true)
2025.06.27 14:19:33 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/commons/commons-compress/1.8.1/commons-compress-1.8.1-sources.jar
2025.06.27 14:19:33 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/commons/commons-compress/1.8.1/commons-compress-1.8.1-sources.jar
2025.06.27 14:19:33 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/commons/commons-compress/1.8.1/commons-compress-1.8.1-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 7.3/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 16/377 kB 
2025.06.27 14:19:33 INFO  Progress (1): 32/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 48/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 65/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 81/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 97/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 114/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 130/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 147/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 163/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 179/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 196/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 212/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 229/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 245/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 261/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 278/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 294/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 310/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 327/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 343/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 360/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 376/377 kB
2025.06.27 14:19:33 INFO  Progress (1): 377 kB    
2025.06.27 14:19:33 INFO                      
2025.06.27 14:19:33 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/commons/commons-compress/1.8.1/commons-compress-1.8.1-sources.jar (377 kB at 4.5 MB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.apache.commons:commons-compress:jar:1.8.1:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.xiaomi.data:data-platform-spec-feeds:jar:0.0.1-SNAPSHOT:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.xiaomi.data:data-platform-spec-feeds:jar:0.0.1-SNAPSHOT:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.xiaomi.data:data-platform-spec-feeds:jar:0.0.1-SNAPSHOT:compile (sources = true)
2025.06.27 14:19:33 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/data/data-platform-spec-feeds/0.0.1-SNAPSHOT/data-platform-spec-feeds-0.0.1-20250627.054030-18641-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 7.4/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 16/662 kB 
2025.06.27 14:19:33 INFO  Progress (1): 32/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 48/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 65/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 81/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 98/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 114/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 130/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 147/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 163/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 179/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 196/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 212/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 229/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 245/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 261/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 278/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 294/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 311/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 327/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 343/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 360/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 376/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 392/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 409/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 425/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 442/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 458/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 474/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 491/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 507/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 524/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 540/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 556/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 573/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 589/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 605/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 622/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 638/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 655/662 kB
2025.06.27 14:19:33 INFO  Progress (1): 662 kB    
2025.06.27 14:19:33 INFO                      
2025.06.27 14:19:33 INFO  Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/data/data-platform-spec-feeds/0.0.1-SNAPSHOT/data-platform-spec-feeds-0.0.1-20250627.054030-18641-sources.jar (662 kB at 3.6 MB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.xiaomi.data:data-platform-spec-feeds:jar:0.0.1-SNAPSHOT:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.apache.httpcomponents:httpclient:jar:4.2.2:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.apache.httpcomponents:httpclient:jar:4.2.2:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.apache.httpcomponents:httpclient:jar:4.2.2:compile (sources = true)
2025.06.27 14:19:33 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/httpcomponents/httpclient/4.2.2/httpclient-4.2.2-sources.jar
2025.06.27 14:19:33 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/httpcomponents/httpclient/4.2.2/httpclient-4.2.2-sources.jar
2025.06.27 14:19:33 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/httpcomponents/httpclient/4.2.2/httpclient-4.2.2-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 7.4/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 16/521 kB 
2025.06.27 14:19:33 INFO  Progress (1): 32/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 48/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 65/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 81/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 97/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 114/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 130/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 147/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 163/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 179/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 196/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 212/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 229/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 245/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 261/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 278/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 294/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 310/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 327/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 343/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 360/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 376/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 392/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 409/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 425/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 442/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 458/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 474/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 491/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 507/521 kB
2025.06.27 14:19:33 INFO  Progress (1): 521 kB    
2025.06.27 14:19:33 INFO                      
2025.06.27 14:19:33 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/httpcomponents/httpclient/4.2.2/httpclient-4.2.2-sources.jar (521 kB at 8.0 MB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.apache.httpcomponents:httpclient:jar:4.2.2:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.apache.avro:avro:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.apache.avro:avro:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.apache.avro:avro:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:33 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/avro/avro/1.8.2/avro-1.8.2-sources.jar
2025.06.27 14:19:33 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/avro/avro/1.8.2/avro-1.8.2-sources.jar
2025.06.27 14:19:33 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/avro/avro/1.8.2/avro-1.8.2-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 7.4/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 16/301 kB 
2025.06.27 14:19:33 INFO  Progress (1): 32/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 48/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 65/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 81/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 97/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 114/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 130/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 147/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 163/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 179/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 196/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 212/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 229/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 245/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 261/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 278/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 294/301 kB
2025.06.27 14:19:33 INFO  Progress (1): 301 kB    
2025.06.27 14:19:33 INFO                      
2025.06.27 14:19:33 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/avro/avro/1.8.2/avro-1.8.2-sources.jar (301 kB at 7.5 MB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.apache.avro:avro:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.twitter.common:collections:jar:0.0.69:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.twitter.common:collections:jar:0.0.69:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.twitter.common:collections:jar:0.0.69:compile (sources = true)
2025.06.27 14:19:33 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/collections/0.0.69/collections-0.0.69-sources.jar
2025.06.27 14:19:33 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/collections/0.0.69/collections-0.0.69-sources.jar
2025.06.27 14:19:33 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/collections/0.0.69/collections-0.0.69-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 7.4/7.5 kB
2025.06.27 14:19:33 INFO  Progress (1): 7.5 kB    
2025.06.27 14:19:33 INFO                      
2025.06.27 14:19:33 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/collections/0.0.69/collections-0.0.69-sources.jar (7.5 kB at 279 kB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.twitter.common:collections:jar:0.0.69:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.apache.avro:avro-protobuf:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.apache.avro:avro-protobuf:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.apache.avro:avro-protobuf:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:33 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/avro/avro-protobuf/1.8.2/avro-protobuf-1.8.2-sources.jar
2025.06.27 14:19:33 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/avro/avro-protobuf/1.8.2/avro-protobuf-1.8.2-sources.jar
2025.06.27 14:19:33 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/avro/avro-protobuf/1.8.2/avro-protobuf-1.8.2-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 7.3/13 kB
2025.06.27 14:19:33 INFO  Progress (1): 13 kB    
2025.06.27 14:19:33 INFO                     
2025.06.27 14:19:33 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/avro/avro-protobuf/1.8.2/avro-protobuf-1.8.2-sources.jar (13 kB at 631 kB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.apache.avro:avro-protobuf:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.2.8:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.2.8:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.2.8:compile (sources = true)
2025.06.27 14:19:33 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-thrift-api/1.2.8/galaxy-thrift-api-1.2.8-sources.jar
2025.06.27 14:19:33 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/infra/galaxy/galaxy-thrift-api/1.2.8/galaxy-thrift-api-1.2.8-sources.jar
2025.06.27 14:19:33 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/infra/galaxy/galaxy-thrift-api/1.2.8/galaxy-thrift-api-1.2.8-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.1/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.1/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.1/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.1/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.1/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.1/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.2/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.2/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.2/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.2/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.2/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.2/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.3/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.3/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.3/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.3/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.3/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.3/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.4/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.4/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.4/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.4/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.4/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.4/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.5/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.5/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.5/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.5/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.5/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.5/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.6/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.6/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.6/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.6/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.6/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.6/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.7/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.7/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.7/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.7/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.7/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.7/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.8/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.8/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.8/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.8/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.8/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.8/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.9/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.9/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.9/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.9/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.9/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.9/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 0.9/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.0/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.1/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.1/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.1/1.1 MB
2025.06.27 14:19:33 INFO  Progress (1): 1.1 MB    
2025.06.27 14:19:33 INFO                      
2025.06.27 14:19:33 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/infra/galaxy/galaxy-thrift-api/1.2.8/galaxy-thrift-api-1.2.8-sources.jar (1.1 MB at 9.5 MB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.2.8:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.twitter.common:jdk-logging:jar:0.0.41:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.twitter.common:jdk-logging:jar:0.0.41:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.twitter.common:jdk-logging:jar:0.0.41:compile (sources = true)
2025.06.27 14:19:33 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/jdk-logging/0.0.41/jdk-logging-0.0.41-sources.jar
2025.06.27 14:19:33 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/jdk-logging/0.0.41/jdk-logging-0.0.41-sources.jar
2025.06.27 14:19:33 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/jdk-logging/0.0.41/jdk-logging-0.0.41-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 3.0 kB
2025.06.27 14:19:33 INFO                      
2025.06.27 14:19:33 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/jdk-logging/0.0.41/jdk-logging-0.0.41-sources.jar (3.0 kB at 99 kB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.twitter.common:jdk-logging:jar:0.0.41:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.twitter.common:util-executor-service-shutdown:jar:0.0.47:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.twitter.common:util-executor-service-shutdown:jar:0.0.47:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: com.twitter.common:util-executor-service-shutdown:jar:0.0.47:compile (sources = true)
2025.06.27 14:19:33 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/util-executor-service-shutdown/0.0.47/util-executor-service-shutdown-0.0.47-sources.jar
2025.06.27 14:19:33 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/util-executor-service-shutdown/0.0.47/util-executor-service-shutdown-0.0.47-sources.jar
2025.06.27 14:19:33 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/util-executor-service-shutdown/0.0.47/util-executor-service-shutdown-0.0.47-sources.jar
2025.06.27 14:19:33 INFO  Progress (1): 2.0 kB
2025.06.27 14:19:33 INFO                      
2025.06.27 14:19:33 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/util-executor-service-shutdown/0.0.47/util-executor-service-shutdown-0.0.47-sources.jar (2.0 kB at 72 kB/s)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: com.twitter.common:util-executor-service-shutdown:jar:0.0.47:compile (sources = true)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.tukaani:xz:jar:1.5:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] SUCCESS: org.tukaani:xz:jar:1.5:compile (sources = false)
2025.06.27 14:19:33 INFO  [INFO] Resolving artifact: org.tukaani:xz:jar:1.5:compile (sources = true)
2025.06.27 14:19:34 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/tukaani/xz/1.5/xz-1.5-sources.jar
2025.06.27 14:19:34 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/tukaani/xz/1.5/xz-1.5-sources.jar
2025.06.27 14:19:34 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/tukaani/xz/1.5/xz-1.5-sources.jar
2025.06.27 14:19:34 INFO  Progress (1): 7.4/118 kB
2025.06.27 14:19:34 INFO  Progress (1): 16/118 kB 
2025.06.27 14:19:34 INFO  Progress (1): 32/118 kB
2025.06.27 14:19:34 INFO  Progress (1): 48/118 kB
2025.06.27 14:19:34 INFO  Progress (1): 65/118 kB
2025.06.27 14:19:34 INFO  Progress (1): 81/118 kB
2025.06.27 14:19:34 INFO  Progress (1): 98/118 kB
2025.06.27 14:19:34 INFO  Progress (1): 114/118 kB
2025.06.27 14:19:34 INFO  Progress (1): 118 kB    
2025.06.27 14:19:34 INFO                      
2025.06.27 14:19:34 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/tukaani/xz/1.5/xz-1.5-sources.jar (118 kB at 3.7 MB/s)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: org.tukaani:xz:jar:1.5:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: javax.annotation:javax.annotation-api:jar:1.3.2:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: javax.annotation:javax.annotation-api:jar:1.3.2:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: javax.annotation:javax.annotation-api:jar:1.3.2:compile (sources = true)
2025.06.27 14:19:34 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2-sources.jar
2025.06.27 14:19:34 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2-sources.jar
2025.06.27 14:19:34 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2-sources.jar
2025.06.27 14:19:34 INFO  Progress (1): 7.3/42 kB
2025.06.27 14:19:34 INFO  Progress (1): 16/42 kB 
2025.06.27 14:19:34 INFO  Progress (1): 32/42 kB
2025.06.27 14:19:34 INFO  Progress (1): 42 kB   
2025.06.27 14:19:34 INFO                     
2025.06.27 14:19:34 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2-sources.jar (42 kB at 2.0 MB/s)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: javax.annotation:javax.annotation-api:jar:1.3.2:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: dnsjava:dnsjava:jar:2.1.8:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: dnsjava:dnsjava:jar:2.1.8:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: dnsjava:dnsjava:jar:2.1.8:compile (sources = true)
2025.06.27 14:19:34 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/dnsjava/dnsjava/2.1.8/dnsjava-2.1.8-sources.jar
2025.06.27 14:19:34 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/dnsjava/dnsjava/2.1.8/dnsjava-2.1.8-sources.jar
2025.06.27 14:19:34 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/dnsjava/dnsjava/2.1.8/dnsjava-2.1.8-sources.jar
2025.06.27 14:19:34 INFO  Progress (1): 7.4/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 16/205 kB 
2025.06.27 14:19:34 INFO  Progress (1): 32/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 48/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 65/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 81/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 97/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 114/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 130/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 147/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 163/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 179/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 196/205 kB
2025.06.27 14:19:34 INFO  Progress (1): 205 kB    
2025.06.27 14:19:34 INFO                      
2025.06.27 14:19:34 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/dnsjava/dnsjava/2.1.8/dnsjava-2.1.8-sources.jar (205 kB at 5.4 MB/s)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: dnsjava:dnsjava:jar:2.1.8:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: com.google.inject:guice:jar:3.0:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: com.google.inject:guice:jar:3.0:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: com.google.inject:guice:jar:3.0:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: com.google.inject:guice:jar:3.0:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: commons-logging:commons-logging:jar:1.1.1:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: commons-logging:commons-logging:jar:1.1.1:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: commons-logging:commons-logging:jar:1.1.1:compile (sources = true)
2025.06.27 14:19:34 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1-sources.jar
2025.06.27 14:19:34 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1-sources.jar
2025.06.27 14:19:34 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1-sources.jar
2025.06.27 14:19:34 INFO  Progress (1): 7.3/75 kB
2025.06.27 14:19:34 INFO  Progress (1): 16/75 kB 
2025.06.27 14:19:34 INFO  Progress (1): 32/75 kB
2025.06.27 14:19:34 INFO  Progress (1): 48/75 kB
2025.06.27 14:19:34 INFO  Progress (1): 65/75 kB
2025.06.27 14:19:34 INFO  Progress (1): 75 kB   
2025.06.27 14:19:34 INFO                     
2025.06.27 14:19:34 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1-sources.jar (75 kB at 3.7 MB/s)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: commons-logging:commons-logging:jar:1.1.1:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-log4j12:jar:1.6.1:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: org.slf4j:slf4j-log4j12:jar:1.6.1:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-log4j12:jar:1.6.1:compile (sources = true)
2025.06.27 14:19:34 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/slf4j/slf4j-log4j12/1.6.1/slf4j-log4j12-1.6.1-sources.jar
2025.06.27 14:19:34 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/slf4j/slf4j-log4j12/1.6.1/slf4j-log4j12-1.6.1-sources.jar
2025.06.27 14:19:34 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/slf4j/slf4j-log4j12/1.6.1/slf4j-log4j12-1.6.1-sources.jar
2025.06.27 14:19:34 INFO  Progress (1): 7.4/9.5 kB
2025.06.27 14:19:34 INFO  Progress (1): 9.5 kB    
2025.06.27 14:19:34 INFO                      
2025.06.27 14:19:34 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/slf4j/slf4j-log4j12/1.6.1/slf4j-log4j12-1.6.1-sources.jar (9.5 kB at 477 kB/s)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: org.slf4j:slf4j-log4j12:jar:1.6.1:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: com.thoughtworks.paranamer:paranamer:jar:2.7:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: com.thoughtworks.paranamer:paranamer:jar:2.7:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: com.thoughtworks.paranamer:paranamer:jar:2.7:compile (sources = true)
2025.06.27 14:19:34 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/thoughtworks/paranamer/paranamer/2.7/paranamer-2.7-sources.jar
2025.06.27 14:19:34 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/thoughtworks/paranamer/paranamer/2.7/paranamer-2.7-sources.jar
2025.06.27 14:19:34 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/thoughtworks/paranamer/paranamer/2.7/paranamer-2.7-sources.jar
2025.06.27 14:19:34 INFO  Progress (1): 7.4/27 kB
2025.06.27 14:19:34 INFO  Progress (1): 16/27 kB 
2025.06.27 14:19:34 INFO  Progress (1): 27 kB   
2025.06.27 14:19:34 INFO                     
2025.06.27 14:19:34 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/thoughtworks/paranamer/paranamer/2.7/paranamer-2.7-sources.jar (27 kB at 1.3 MB/s)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: com.thoughtworks.paranamer:paranamer:jar:2.7:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: javax.servlet:javax.servlet-api:jar:3.0.1:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: javax.servlet:javax.servlet-api:jar:3.0.1:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: javax.servlet:javax.servlet-api:jar:3.0.1:compile (sources = true)
2025.06.27 14:19:34 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1-sources.jar
2025.06.27 14:19:34 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1-sources.jar
2025.06.27 14:19:34 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1-sources.jar
2025.06.27 14:19:34 INFO  Progress (1): 7.3/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 16/213 kB 
2025.06.27 14:19:34 INFO  Progress (1): 32/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 48/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 65/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 81/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 97/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 114/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 130/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 147/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 163/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 179/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 196/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 212/213 kB
2025.06.27 14:19:34 INFO  Progress (1): 213 kB    
2025.06.27 14:19:34 INFO                      
2025.06.27 14:19:34 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1-sources.jar (213 kB at 6.3 MB/s)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: javax.servlet:javax.servlet-api:jar:3.0.1:compile (sources = true)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:34 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:34 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-common/2.1.13/galaxy-lcs-common-2.1.13-sources.jar
2025.06.27 14:19:34 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-common/2.1.13/galaxy-lcs-common-2.1.13-sources.jar
2025.06.27 14:19:34 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-common/2.1.13/galaxy-lcs-common-2.1.13-sources.jar
2025.06.27 14:19:35 INFO  Downloading from public: https://pkgs.d.xiaomi.net/artifactory/maven-public-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-common/2.1.13/galaxy-lcs-common-2.1.13-sources.jar
2025.06.27 14:19:35 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:35 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:sources:2.1.13 (absent): Could not find artifact com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:sources:2.1.13 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:35 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:35 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:35 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:35 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:35 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:35 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:35 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:35 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:35 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:35 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:35 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:35 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:35 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:35 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:35 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:35 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:35 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:35 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:35 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:35 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:35 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: Could not find artifact com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:sources:2.1.13 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.connector.basic.ArtifactTransportListener.transferFailed (ArtifactTransportListener.java:42)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector$TaskRunner.run (BasicRepositoryConnector.java:417)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector.get (BasicRepositoryConnector.java:260)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:537)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:35 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:35 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:35 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:35 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:35 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:35 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:35 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:35 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:35 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:35 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:35 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:35 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:35 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:35 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:35 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:35 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:35 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:35 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:35 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:35 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:35 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:35 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:35 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:35 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:35 INFO  [INFO] Resolving artifact: com.twitter.common:quantity:jar:0.0.66:compile (sources = false)
2025.06.27 14:19:35 INFO  [INFO] SUCCESS: com.twitter.common:quantity:jar:0.0.66:compile (sources = false)
2025.06.27 14:19:35 INFO  [INFO] Resolving artifact: com.twitter.common:quantity:jar:0.0.66:compile (sources = true)
2025.06.27 14:19:35 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/quantity/0.0.66/quantity-0.0.66-sources.jar
2025.06.27 14:19:35 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/quantity/0.0.66/quantity-0.0.66-sources.jar
2025.06.27 14:19:35 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/quantity/0.0.66/quantity-0.0.66-sources.jar
2025.06.27 14:19:35 INFO  Progress (1): 5.3 kB
2025.06.27 14:19:35 INFO                      
2025.06.27 14:19:35 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/quantity/0.0.66/quantity-0.0.66-sources.jar (5.3 kB at 222 kB/s)
2025.06.27 14:19:35 INFO  [INFO] SUCCESS: com.twitter.common:quantity:jar:0.0.66:compile (sources = true)
2025.06.27 14:19:35 INFO  [INFO] Resolving artifact: org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile (sources = false)
2025.06.27 14:19:35 INFO  [INFO] SUCCESS: org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile (sources = false)
2025.06.27 14:19:35 INFO  [INFO] Resolving artifact: org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile (sources = true)
2025.06.27 14:19:35 INFO  [INFO] SUCCESS: org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile (sources = true)
2025.06.27 14:19:35 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:1.0.0:compile (sources = false)
2025.06.27 14:19:35 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:1.0.0:compile (sources = false)
2025.06.27 14:19:35 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:1.0.0:compile (sources = true)
2025.06.27 14:19:35 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-schema-thrift/1.0.0/galaxy-schema-thrift-1.0.0-sources.jar
2025.06.27 14:19:35 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/infra/galaxy/galaxy-schema-thrift/1.0.0/galaxy-schema-thrift-1.0.0-sources.jar
2025.06.27 14:19:35 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/infra/galaxy/galaxy-schema-thrift/1.0.0/galaxy-schema-thrift-1.0.0-sources.jar
2025.06.27 14:19:36 INFO  Downloading from public: https://pkgs.d.xiaomi.net/artifactory/maven-public-virtual/com/xiaomi/infra/galaxy/galaxy-schema-thrift/1.0.0/galaxy-schema-thrift-1.0.0-sources.jar
2025.06.27 14:19:36 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:1.0.0:compile (sources = true)
2025.06.27 14:19:36 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:sources:1.0.0 (absent): Could not find artifact com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:sources:1.0.0 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:36 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:36 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:36 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:36 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:36 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:36 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:36 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:36 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:36 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:36 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:36 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:36 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:36 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:36 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:36 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:36 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:36 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:36 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:36 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:36 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:36 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: Could not find artifact com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:sources:1.0.0 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.connector.basic.ArtifactTransportListener.transferFailed (ArtifactTransportListener.java:42)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector$TaskRunner.run (BasicRepositoryConnector.java:417)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector.get (BasicRepositoryConnector.java:260)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:537)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:36 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:36 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:36 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:36 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:36 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:36 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:36 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:36 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:36 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:36 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:36 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:36 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:36 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:36 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:36 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:36 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:36 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:36 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:36 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:36 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:36 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:36 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:36 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:36 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.codahale.metrics:metrics-core:jar:3.0.2:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.codahale.metrics:metrics-core:jar:3.0.2:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.codahale.metrics:metrics-core:jar:3.0.2:compile (sources = true)
2025.06.27 14:19:36 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/codahale/metrics/metrics-core/3.0.2/metrics-core-3.0.2-sources.jar
2025.06.27 14:19:36 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/codahale/metrics/metrics-core/3.0.2/metrics-core-3.0.2-sources.jar
2025.06.27 14:19:36 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/codahale/metrics/metrics-core/3.0.2/metrics-core-3.0.2-sources.jar
2025.06.27 14:19:36 INFO  Progress (1): 7.4/44 kB
2025.06.27 14:19:36 INFO  Progress (1): 16/44 kB 
2025.06.27 14:19:36 INFO  Progress (1): 32/44 kB
2025.06.27 14:19:36 INFO  Progress (1): 44 kB   
2025.06.27 14:19:36 INFO                     
2025.06.27 14:19:36 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/codahale/metrics/metrics-core/3.0.2/metrics-core-3.0.2-sources.jar (44 kB at 1.5 MB/s)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.codahale.metrics:metrics-core:jar:3.0.2:compile (sources = true)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.google.code.gson:gson:jar:2.2.4:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.google.code.gson:gson:jar:2.2.4:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.google.code.gson:gson:jar:2.2.4:compile (sources = true)
2025.06.27 14:19:36 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/google/code/gson/gson/2.2.4/gson-2.2.4-sources.jar
2025.06.27 14:19:36 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/google/code/gson/gson/2.2.4/gson-2.2.4-sources.jar
2025.06.27 14:19:36 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/google/code/gson/gson/2.2.4/gson-2.2.4-sources.jar
2025.06.27 14:19:36 INFO  Progress (1): 7.4/128 kB
2025.06.27 14:19:36 INFO  Progress (1): 16/128 kB 
2025.06.27 14:19:36 INFO  Progress (1): 32/128 kB
2025.06.27 14:19:36 INFO  Progress (1): 48/128 kB
2025.06.27 14:19:36 INFO  Progress (1): 65/128 kB
2025.06.27 14:19:36 INFO  Progress (1): 81/128 kB
2025.06.27 14:19:36 INFO  Progress (1): 97/128 kB
2025.06.27 14:19:36 INFO  Progress (1): 114/128 kB
2025.06.27 14:19:36 INFO  Progress (1): 128 kB    
2025.06.27 14:19:36 INFO                      
2025.06.27 14:19:36 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/google/code/gson/gson/2.2.4/gson-2.2.4-sources.jar (128 kB at 4.7 MB/s)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.google.code.gson:gson:jar:2.2.4:compile (sources = true)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.github.luben:zstd-jni:jar:1.5.0-4:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.github.luben:zstd-jni:jar:1.5.0-4:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.github.luben:zstd-jni:jar:1.5.0-4:compile (sources = true)
2025.06.27 14:19:36 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/github/luben/zstd-jni/1.5.0-4/zstd-jni-1.5.0-4-sources.jar
2025.06.27 14:19:36 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/github/luben/zstd-jni/1.5.0-4/zstd-jni-1.5.0-4-sources.jar
2025.06.27 14:19:36 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/github/luben/zstd-jni/1.5.0-4/zstd-jni-1.5.0-4-sources.jar
2025.06.27 14:19:36 INFO  Progress (1): 7.4/28 kB
2025.06.27 14:19:36 INFO  Progress (1): 16/28 kB 
2025.06.27 14:19:36 INFO  Progress (1): 28 kB   
2025.06.27 14:19:36 INFO                     
2025.06.27 14:19:36 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/github/luben/zstd-jni/1.5.0-4/zstd-jni-1.5.0-4-sources.jar (28 kB at 1.3 MB/s)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.github.luben:zstd-jni:jar:1.5.0-4:compile (sources = true)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.twitter.common:stat-registry:jar:0.0.24:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.twitter.common:stat-registry:jar:0.0.24:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.twitter.common:stat-registry:jar:0.0.24:compile (sources = true)
2025.06.27 14:19:36 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/stat-registry/0.0.24/stat-registry-0.0.24-sources.jar
2025.06.27 14:19:36 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/stat-registry/0.0.24/stat-registry-0.0.24-sources.jar
2025.06.27 14:19:36 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/stat-registry/0.0.24/stat-registry-0.0.24-sources.jar
2025.06.27 14:19:36 INFO  Progress (1): 2.0 kB
2025.06.27 14:19:36 INFO                      
2025.06.27 14:19:36 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/stat-registry/0.0.24/stat-registry-0.0.24-sources.jar (2.0 kB at 49 kB/s)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.twitter.common:stat-registry:jar:0.0.24:compile (sources = true)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: org.apache.commons:commons-lang3:jar:3.1:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: org.apache.commons:commons-lang3:jar:3.1:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: org.apache.commons:commons-lang3:jar:3.1:compile (sources = true)
2025.06.27 14:19:36 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/commons/commons-lang3/3.1/commons-lang3-3.1-sources.jar
2025.06.27 14:19:36 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/commons/commons-lang3/3.1/commons-lang3-3.1-sources.jar
2025.06.27 14:19:36 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/commons/commons-lang3/3.1/commons-lang3-3.1-sources.jar
2025.06.27 14:19:36 INFO  Progress (1): 7.4/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 16/393 kB 
2025.06.27 14:19:36 INFO  Progress (1): 32/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 48/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 65/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 81/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 97/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 114/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 130/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 147/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 163/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 179/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 196/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 212/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 229/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 245/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 261/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 278/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 294/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 310/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 327/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 343/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 360/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 376/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 392/393 kB
2025.06.27 14:19:36 INFO  Progress (1): 393 kB    
2025.06.27 14:19:36 INFO                      
2025.06.27 14:19:36 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/commons/commons-lang3/3.1/commons-lang3-3.1-sources.jar (393 kB at 7.6 MB/s)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: org.apache.commons:commons-lang3:jar:3.1:compile (sources = true)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.fasterxml.uuid:java-uuid-generator:jar:3.1.3:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.fasterxml.uuid:java-uuid-generator:jar:3.1.3:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.fasterxml.uuid:java-uuid-generator:jar:3.1.3:compile (sources = true)
2025.06.27 14:19:36 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/fasterxml/uuid/java-uuid-generator/3.1.3/java-uuid-generator-3.1.3-sources.jar
2025.06.27 14:19:36 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/fasterxml/uuid/java-uuid-generator/3.1.3/java-uuid-generator-3.1.3-sources.jar
2025.06.27 14:19:36 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/fasterxml/uuid/java-uuid-generator/3.1.3/java-uuid-generator-3.1.3-sources.jar
2025.06.27 14:19:36 INFO  Progress (1): 7.3/45 kB
2025.06.27 14:19:36 INFO  Progress (1): 16/45 kB 
2025.06.27 14:19:36 INFO  Progress (1): 32/45 kB
2025.06.27 14:19:36 INFO  Progress (1): 45 kB   
2025.06.27 14:19:36 INFO                     
2025.06.27 14:19:36 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/fasterxml/uuid/java-uuid-generator/3.1.3/java-uuid-generator-3.1.3-sources.jar (45 kB at 1.2 MB/s)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.fasterxml.uuid:java-uuid-generator:jar:3.1.3:compile (sources = true)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.twitter.common:base:jar:0.0.82:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.twitter.common:base:jar:0.0.82:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.twitter.common:base:jar:0.0.82:compile (sources = true)
2025.06.27 14:19:36 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/base/0.0.82/base-0.0.82-sources.jar
2025.06.27 14:19:36 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/base/0.0.82/base-0.0.82-sources.jar
2025.06.27 14:19:36 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/base/0.0.82/base-0.0.82-sources.jar
2025.06.27 14:19:36 INFO  Progress (1): 7.4/21 kB
2025.06.27 14:19:36 INFO  Progress (1): 16/21 kB 
2025.06.27 14:19:36 INFO  Progress (1): 21 kB   
2025.06.27 14:19:36 INFO                     
2025.06.27 14:19:36 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/base/0.0.82/base-0.0.82-sources.jar (21 kB at 531 kB/s)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.twitter.common:base:jar:0.0.82:compile (sources = true)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.google.code.findbugs:jsr305:jar:1.3.9:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] SUCCESS: com.google.code.findbugs:jsr305:jar:1.3.9:compile (sources = false)
2025.06.27 14:19:36 INFO  [INFO] Resolving artifact: com.google.code.findbugs:jsr305:jar:1.3.9:compile (sources = true)
2025.06.27 14:19:36 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9-sources.jar
2025.06.27 14:19:36 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9-sources.jar
2025.06.27 14:19:36 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9-sources.jar
2025.06.27 14:19:38 INFO  Downloading from public: https://pkgs.d.xiaomi.net/artifactory/maven-public-virtual/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9-sources.jar
2025.06.27 14:19:38 INFO  [ERROR] FAILURE com.google.code.findbugs:jsr305:jar:1.3.9:compile (sources = true)
2025.06.27 14:19:38 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.google.code.findbugs:jsr305:jar:sources:1.3.9 (absent): Could not find artifact com.google.code.findbugs:jsr305:jar:sources:1.3.9 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:38 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:38 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:38 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:38 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:38 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:38 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:38 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:38 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:38 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:38 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:38 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:38 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:38 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:38 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:38 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:38 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:38 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:38 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:38 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:38 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:38 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: Could not find artifact com.google.code.findbugs:jsr305:jar:sources:1.3.9 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.connector.basic.ArtifactTransportListener.transferFailed (ArtifactTransportListener.java:42)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector$TaskRunner.run (BasicRepositoryConnector.java:417)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector.get (BasicRepositoryConnector.java:260)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:537)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:38 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:38 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:38 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:38 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:38 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:38 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:38 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:38 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:38 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:38 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:38 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:38 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:38 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:38 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:38 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:38 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:38 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:38 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:38 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:38 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:38 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:38 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:38 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:38 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.apache.httpcomponents:httpcore:jar:4.2.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.apache.httpcomponents:httpcore:jar:4.2.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.apache.httpcomponents:httpcore:jar:4.2.2:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/httpcomponents/httpcore/4.2.2/httpcore-4.2.2-sources.jar
2025.06.27 14:19:38 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/httpcomponents/httpcore/4.2.2/httpcore-4.2.2-sources.jar
2025.06.27 14:19:38 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/httpcomponents/httpcore/4.2.2/httpcore-4.2.2-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 7.4/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 16/322 kB 
2025.06.27 14:19:38 INFO  Progress (1): 32/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 48/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 65/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 81/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 97/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 114/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 130/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 147/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 163/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 179/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 196/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 212/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 229/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 245/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 261/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 278/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 294/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 310/322 kB
2025.06.27 14:19:38 INFO  Progress (1): 322 kB    
2025.06.27 14:19:38 INFO                      
2025.06.27 14:19:38 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/httpcomponents/httpcore/4.2.2/httpcore-4.2.2-sources.jar (322 kB at 6.7 MB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.apache.httpcomponents:httpcore:jar:4.2.2:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-talos-sdk:jar:2.7.0.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-talos-sdk:jar:2.7.0.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-talos-sdk:jar:2.7.0.2:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-talos-sdk/2.7.0.2/galaxy-talos-sdk-2.7.0.2-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 7.3/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 16/857 kB 
2025.06.27 14:19:38 INFO  Progress (1): 32/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 48/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 65/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 81/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 97/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 114/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 130/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 147/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 163/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 179/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 196/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 212/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 229/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 245/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 261/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 278/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 294/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 310/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 327/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 343/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 360/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 376/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 392/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 409/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 425/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 442/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 458/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 474/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 491/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 507/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 523/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 540/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 556/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 573/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 589/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 605/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 622/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 638/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 655/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 671/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 687/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 704/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 720/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 736/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 753/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 769/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 786/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 802/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 818/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 835/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 851/857 kB
2025.06.27 14:19:38 INFO  Progress (1): 857 kB    
2025.06.27 14:19:38 INFO                      
2025.06.27 14:19:38 INFO  Downloaded from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-talos-sdk/2.7.0.2/galaxy-talos-sdk-2.7.0.2-sources.jar (857 kB at 8.8 MB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-talos-sdk:jar:2.7.0.2:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-annotations:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-annotations:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-annotations:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2-sources.jar
2025.06.27 14:19:38 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2-sources.jar
2025.06.27 14:19:38 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 7.3/82 kB
2025.06.27 14:19:38 INFO  Progress (1): 16/82 kB 
2025.06.27 14:19:38 INFO  Progress (1): 32/82 kB
2025.06.27 14:19:38 INFO  Progress (1): 48/82 kB
2025.06.27 14:19:38 INFO  Progress (1): 65/82 kB
2025.06.27 14:19:38 INFO  Progress (1): 81/82 kB
2025.06.27 14:19:38 INFO  Progress (1): 82 kB   
2025.06.27 14:19:38 INFO                     
2025.06.27 14:19:38 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2-sources.jar (82 kB at 3.7 MB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-annotations:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: commons-lang:commons-lang:jar:2.6:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: commons-lang:commons-lang:jar:2.6:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: commons-lang:commons-lang:jar:2.6:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: commons-lang:commons-lang:jar:2.6:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: commons-io:commons-io:jar:2.11.0:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: commons-io:commons-io:jar:2.11.0:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: commons-io:commons-io:jar:2.11.0:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/commons-io/commons-io/2.11.0/commons-io-2.11.0-sources.jar
2025.06.27 14:19:38 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/commons-io/commons-io/2.11.0/commons-io-2.11.0-sources.jar
2025.06.27 14:19:38 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-io/commons-io/2.11.0/commons-io-2.11.0-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 7.4/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 16/399 kB 
2025.06.27 14:19:38 INFO  Progress (1): 32/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 48/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 65/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 81/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 97/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 114/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 130/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 147/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 163/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 179/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 196/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 212/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 229/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 245/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 261/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 278/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 294/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 310/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 327/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 343/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 360/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 376/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 392/399 kB
2025.06.27 14:19:38 INFO  Progress (1): 399 kB    
2025.06.27 14:19:38 INFO                      
2025.06.27 14:19:38 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-io/commons-io/2.11.0/commons-io-2.11.0-sources.jar (399 kB at 7.7 MB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: commons-io:commons-io:jar:2.11.0:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.apache.thrift:thrift:jar:0.5.0-fix-thrift2402:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.apache.thrift:thrift:jar:0.5.0-fix-thrift2402:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.apache.thrift:thrift:jar:0.5.0-fix-thrift2402:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/thrift/thrift/0.5.0-fix-thrift2402/thrift-0.5.0-fix-thrift2402-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 7.3/116 kB
2025.06.27 14:19:38 INFO  Progress (1): 16/116 kB 
2025.06.27 14:19:38 INFO  Progress (1): 32/116 kB
2025.06.27 14:19:38 INFO  Progress (1): 48/116 kB
2025.06.27 14:19:38 INFO  Progress (1): 65/116 kB
2025.06.27 14:19:38 INFO  Progress (1): 81/116 kB
2025.06.27 14:19:38 INFO  Progress (1): 97/116 kB
2025.06.27 14:19:38 INFO  Progress (1): 114/116 kB
2025.06.27 14:19:38 INFO  Progress (1): 116 kB    
2025.06.27 14:19:38 INFO                      
2025.06.27 14:19:38 INFO  Downloaded from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/thrift/thrift/0.5.0-fix-thrift2402/thrift-0.5.0-fix-thrift2402-sources.jar (116 kB at 4.8 MB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.apache.thrift:thrift:jar:0.5.0-fix-thrift2402:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-client-java:jar:1.2.5:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-client-java:jar:1.2.5:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-client-java:jar:1.2.5:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-client-java/1.2.5/galaxy-client-java-1.2.5-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 7.3/60 kB
2025.06.27 14:19:38 INFO  Progress (1): 16/60 kB 
2025.06.27 14:19:38 INFO  Progress (1): 32/60 kB
2025.06.27 14:19:38 INFO  Progress (1): 48/60 kB
2025.06.27 14:19:38 INFO  Progress (1): 60 kB   
2025.06.27 14:19:38 INFO                     
2025.06.27 14:19:38 INFO  Downloaded from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-client-java/1.2.5/galaxy-client-java-1.2.5-sources.jar (60 kB at 2.5 MB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-client-java:jar:1.2.5:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-core:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-core:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-core:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2-sources.jar
2025.06.27 14:19:38 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2-sources.jar
2025.06.27 14:19:38 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 7.3/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 16/466 kB 
2025.06.27 14:19:38 INFO  Progress (1): 32/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 48/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 65/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 81/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 97/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 114/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 130/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 147/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 163/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 179/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 196/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 212/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 229/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 245/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 261/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 278/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 294/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 310/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 327/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 343/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 360/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 376/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 392/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 409/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 425/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 442/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 458/466 kB
2025.06.27 14:19:38 INFO  Progress (1): 466 kB    
2025.06.27 14:19:38 INFO                      
2025.06.27 14:19:38 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2-sources.jar (466 kB at 7.6 MB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-core:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.apache.avro:avro-thrift:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.apache.avro:avro-thrift:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.apache.avro:avro-thrift:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/avro/avro-thrift/1.8.2/avro-thrift-1.8.2-sources.jar
2025.06.27 14:19:38 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/avro/avro-thrift/1.8.2/avro-thrift-1.8.2-sources.jar
2025.06.27 14:19:38 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/avro/avro-thrift/1.8.2/avro-thrift-1.8.2-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 7.4/12 kB
2025.06.27 14:19:38 INFO  Progress (1): 12 kB    
2025.06.27 14:19:38 INFO                     
2025.06.27 14:19:38 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/avro/avro-thrift/1.8.2/avro-thrift-1.8.2-sources.jar (12 kB at 609 kB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.apache.avro:avro-thrift:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.jsoup:jsoup:jar:1.13.1:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.jsoup:jsoup:jar:1.13.1:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.jsoup:jsoup:jar:1.13.1:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/jsoup/jsoup/1.13.1/jsoup-1.13.1-sources.jar
2025.06.27 14:19:38 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/jsoup/jsoup/1.13.1/jsoup-1.13.1-sources.jar
2025.06.27 14:19:38 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/jsoup/jsoup/1.13.1/jsoup-1.13.1-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 7.4/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 16/193 kB 
2025.06.27 14:19:38 INFO  Progress (1): 32/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 48/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 65/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 81/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 97/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 114/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 130/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 147/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 163/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 179/193 kB
2025.06.27 14:19:38 INFO  Progress (1): 193 kB    
2025.06.27 14:19:38 INFO                      
2025.06.27 14:19:38 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/jsoup/jsoup/1.13.1/jsoup-1.13.1-sources.jar (193 kB at 5.5 MB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.jsoup:jsoup:jar:1.13.1:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.xerial.snappy:snappy-java:jar:1.1.2.6:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.xerial.snappy:snappy-java:jar:1.1.2.6:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: org.xerial.snappy:snappy-java:jar:1.1.2.6:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/xerial/snappy/snappy-java/1.1.2.6/snappy-java-1.1.2.6-sources.jar
2025.06.27 14:19:38 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/xerial/snappy/snappy-java/1.1.2.6/snappy-java-1.1.2.6-sources.jar
2025.06.27 14:19:38 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/xerial/snappy/snappy-java/1.1.2.6/snappy-java-1.1.2.6-sources.jar
2025.06.27 14:19:38 INFO  Progress (1): 0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.1/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.1/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.1/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.1/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.1/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.1/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.2/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.2/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.2/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.2/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.2/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.2/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.3/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.3/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.3/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.3/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.3/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.3/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.4/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.4/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.4/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.4/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.4/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.4/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.5/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.5/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.5/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.5/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.5/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.5/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.6/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.6/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.6/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.6/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.6/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.6/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.7/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.7/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.7/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.7/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.7/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.7/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.8/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.8/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.8/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.8/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.8/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.8/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.9/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.9/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.9/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.9/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.9/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.9/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 0.9/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 1.0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 1.0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 1.0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 1.0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 1.0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 1.0/1.0 MB
2025.06.27 14:19:38 INFO  Progress (1): 1.0 MB    
2025.06.27 14:19:38 INFO                      
2025.06.27 14:19:38 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/xerial/snappy/snappy-java/1.1.2.6/snappy-java-1.1.2.6-sources.jar (1.0 MB at 9.0 MB/s)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: org.xerial.snappy:snappy-java:jar:1.1.2.6:compile (sources = true)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-schema-client:jar:1.0.0:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-schema-client:jar:1.0.0:compile (sources = false)
2025.06.27 14:19:38 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-schema-client:jar:1.0.0:compile (sources = true)
2025.06.27 14:19:38 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-schema-client/1.0.0/galaxy-schema-client-1.0.0-sources.jar
2025.06.27 14:19:39 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/infra/galaxy/galaxy-schema-client/1.0.0/galaxy-schema-client-1.0.0-sources.jar
2025.06.27 14:19:39 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/infra/galaxy/galaxy-schema-client/1.0.0/galaxy-schema-client-1.0.0-sources.jar
2025.06.27 14:19:40 INFO  Downloading from public: https://pkgs.d.xiaomi.net/artifactory/maven-public-virtual/com/xiaomi/infra/galaxy/galaxy-schema-client/1.0.0/galaxy-schema-client-1.0.0-sources.jar
2025.06.27 14:19:40 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-schema-client:jar:1.0.0:compile (sources = true)
2025.06.27 14:19:40 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-schema-client:jar:sources:1.0.0 (absent): Could not find artifact com.xiaomi.infra.galaxy:galaxy-schema-client:jar:sources:1.0.0 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:40 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:40 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:40 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:40 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:40 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:40 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:40 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:40 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:40 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:40 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:40 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:40 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:40 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:40 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:40 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:40 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:40 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:40 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:40 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:40 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:40 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: Could not find artifact com.xiaomi.infra.galaxy:galaxy-schema-client:jar:sources:1.0.0 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.connector.basic.ArtifactTransportListener.transferFailed (ArtifactTransportListener.java:42)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector$TaskRunner.run (BasicRepositoryConnector.java:417)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector.get (BasicRepositoryConnector.java:260)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:537)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:40 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:40 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:40 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:40 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:40 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:40 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:40 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:40 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:40 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:40 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:40 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:40 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:40 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:40 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:40 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:40 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:40 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:40 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:40 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:40 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:40 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:40 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:40 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:40 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: commons-codec:commons-codec:jar:1.4:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] SUCCESS: commons-codec:commons-codec:jar:1.4:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: commons-codec:commons-codec:jar:1.4:compile (sources = true)
2025.06.27 14:19:40 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/commons-codec/commons-codec/1.4/commons-codec-1.4-sources.jar
2025.06.27 14:19:40 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/commons-codec/commons-codec/1.4/commons-codec-1.4-sources.jar
2025.06.27 14:19:40 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-codec/commons-codec/1.4/commons-codec-1.4-sources.jar
2025.06.27 14:19:40 INFO  Progress (1): 7.4/81 kB
2025.06.27 14:19:40 INFO  Progress (1): 16/81 kB 
2025.06.27 14:19:40 INFO  Progress (1): 32/81 kB
2025.06.27 14:19:40 INFO  Progress (1): 48/81 kB
2025.06.27 14:19:40 INFO  Progress (1): 65/81 kB
2025.06.27 14:19:40 INFO  Progress (1): 81/81 kB
2025.06.27 14:19:40 INFO  Progress (1): 81 kB   
2025.06.27 14:19:40 INFO                     
2025.06.27 14:19:40 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-codec/commons-codec/1.4/commons-codec-1.4-sources.jar (81 kB at 2.6 MB/s)
2025.06.27 14:19:40 INFO  [INFO] SUCCESS: commons-codec:commons-codec:jar:1.4:compile (sources = true)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: commons-beanutils:commons-beanutils:jar:1.9.4:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] SUCCESS: commons-beanutils:commons-beanutils:jar:1.9.4:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: commons-beanutils:commons-beanutils:jar:1.9.4:compile (sources = true)
2025.06.27 14:19:40 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4-sources.jar
2025.06.27 14:19:40 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4-sources.jar
2025.06.27 14:19:40 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4-sources.jar
2025.06.27 14:19:40 INFO  Progress (1): 7.3/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 16/310 kB 
2025.06.27 14:19:40 INFO  Progress (1): 32/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 48/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 65/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 81/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 97/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 114/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 130/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 147/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 163/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 179/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 196/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 212/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 229/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 245/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 261/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 278/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 294/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 310/310 kB
2025.06.27 14:19:40 INFO  Progress (1): 310 kB    
2025.06.27 14:19:40 INFO                      
2025.06.27 14:19:40 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4-sources.jar (310 kB at 5.0 MB/s)
2025.06.27 14:19:40 INFO  [INFO] SUCCESS: commons-beanutils:commons-beanutils:jar:1.9.4:compile (sources = true)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: log4j:log4j:jar:1.2.16:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] SUCCESS: log4j:log4j:jar:1.2.16:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: log4j:log4j:jar:1.2.16:compile (sources = true)
2025.06.27 14:19:40 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/log4j/log4j/1.2.16/log4j-1.2.16-sources.jar
2025.06.27 14:19:40 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/log4j/log4j/1.2.16/log4j-1.2.16-sources.jar
2025.06.27 14:19:40 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/log4j/log4j/1.2.16/log4j-1.2.16-sources.jar
2025.06.27 14:19:40 INFO  Progress (1): 7.4/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 16/471 kB 
2025.06.27 14:19:40 INFO  Progress (1): 32/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 48/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 65/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 81/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 97/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 114/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 130/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 147/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 163/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 179/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 196/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 212/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 229/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 245/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 261/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 278/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 294/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 310/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 327/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 343/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 360/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 376/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 392/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 409/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 425/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 442/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 458/471 kB
2025.06.27 14:19:40 INFO  Progress (1): 471 kB    
2025.06.27 14:19:40 INFO                      
2025.06.27 14:19:40 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/log4j/log4j/1.2.16/log4j-1.2.16-sources.jar (471 kB at 6.2 MB/s)
2025.06.27 14:19:40 INFO  [INFO] SUCCESS: log4j:log4j:jar:1.2.16:compile (sources = true)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: javax.inject:javax.inject:jar:1:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] SUCCESS: javax.inject:javax.inject:jar:1:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: javax.inject:javax.inject:jar:1:compile (sources = true)
2025.06.27 14:19:40 INFO  [INFO] SUCCESS: javax.inject:javax.inject:jar:1:compile (sources = true)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:streaming-common:jar:1.0:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:streaming-common:jar:1.0:compile (sources = false)
2025.06.27 14:19:40 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:streaming-common:jar:1.0:compile (sources = true)
2025.06.27 14:19:40 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/streaming-common/1.0/streaming-common-1.0-sources.jar
2025.06.27 14:19:40 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/infra/galaxy/streaming-common/1.0/streaming-common-1.0-sources.jar
2025.06.27 14:19:40 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/infra/galaxy/streaming-common/1.0/streaming-common-1.0-sources.jar
2025.06.27 14:19:42 INFO  Downloading from public: https://pkgs.d.xiaomi.net/artifactory/maven-public-virtual/com/xiaomi/infra/galaxy/streaming-common/1.0/streaming-common-1.0-sources.jar
2025.06.27 14:19:42 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:streaming-common:jar:1.0:compile (sources = true)
2025.06.27 14:19:42 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:streaming-common:jar:sources:1.0 (absent): Could not find artifact com.xiaomi.infra.galaxy:streaming-common:jar:sources:1.0 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:42 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:42 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:42 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:42 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:42 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:42 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:42 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:42 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:42 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:42 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:42 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:42 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:42 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:42 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:42 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:42 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:42 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:42 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:42 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:42 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:42 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: Could not find artifact com.xiaomi.infra.galaxy:streaming-common:jar:sources:1.0 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.connector.basic.ArtifactTransportListener.transferFailed (ArtifactTransportListener.java:42)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector$TaskRunner.run (BasicRepositoryConnector.java:417)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector.get (BasicRepositoryConnector.java:260)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:537)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:42 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:42 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:42 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:42 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:42 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:42 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:42 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:42 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:42 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:42 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:42 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:42 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:42 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:42 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:42 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:42 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:42 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:42 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:42 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:42 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:42 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:42 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:42 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:42 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-api:jar:1.7.28:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: org.slf4j:slf4j-api:jar:1.7.28:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-api:jar:1.7.28:compile (sources = true)
2025.06.27 14:19:42 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/slf4j/slf4j-api/1.7.28/slf4j-api-1.7.28-sources.jar
2025.06.27 14:19:42 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/slf4j/slf4j-api/1.7.28/slf4j-api-1.7.28-sources.jar
2025.06.27 14:19:42 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/slf4j/slf4j-api/1.7.28/slf4j-api-1.7.28-sources.jar
2025.06.27 14:19:42 INFO  Progress (1): 7.4/58 kB
2025.06.27 14:19:42 INFO  Progress (1): 16/58 kB 
2025.06.27 14:19:42 INFO  Progress (1): 32/58 kB
2025.06.27 14:19:42 INFO  Progress (1): 48/58 kB
2025.06.27 14:19:42 INFO  Progress (1): 58 kB   
2025.06.27 14:19:42 INFO                     
2025.06.27 14:19:42 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/slf4j/slf4j-api/1.7.28/slf4j-api-1.7.28-sources.jar (58 kB at 2.1 MB/s)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: org.slf4j:slf4j-api:jar:1.7.28:compile (sources = true)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: com.twitter.common:util:jar:0.0.92:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: com.twitter.common:util:jar:0.0.92:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: com.twitter.common:util:jar:0.0.92:compile (sources = true)
2025.06.27 14:19:42 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/util/0.0.92/util-0.0.92-sources.jar
2025.06.27 14:19:42 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/util/0.0.92/util-0.0.92-sources.jar
2025.06.27 14:19:42 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/util/0.0.92/util-0.0.92-sources.jar
2025.06.27 14:19:42 INFO  Progress (1): 7.4/38 kB
2025.06.27 14:19:42 INFO  Progress (1): 16/38 kB 
2025.06.27 14:19:42 INFO  Progress (1): 32/38 kB
2025.06.27 14:19:42 INFO  Progress (1): 38 kB   
2025.06.27 14:19:42 INFO                     
2025.06.27 14:19:42 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/util/0.0.92/util-0.0.92-sources.jar (38 kB at 1.5 MB/s)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: com.twitter.common:util:jar:0.0.92:compile (sources = true)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: commons-collections:commons-collections:jar:3.2.2:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: commons-collections:commons-collections:jar:3.2.2:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: commons-collections:commons-collections:jar:3.2.2:compile (sources = true)
2025.06.27 14:19:42 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2-sources.jar
2025.06.27 14:19:42 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2-sources.jar
2025.06.27 14:19:42 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2-sources.jar
2025.06.27 14:19:42 INFO  Progress (1): 7.3/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 16/646 kB 
2025.06.27 14:19:42 INFO  Progress (1): 32/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 48/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 65/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 81/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 97/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 114/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 130/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 147/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 163/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 179/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 196/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 212/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 229/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 245/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 261/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 278/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 294/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 310/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 327/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 343/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 360/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 376/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 392/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 409/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 425/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 442/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 458/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 474/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 491/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 507/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 523/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 540/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 556/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 573/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 589/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 605/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 622/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 638/646 kB
2025.06.27 14:19:42 INFO  Progress (1): 646 kB    
2025.06.27 14:19:42 INFO                      
2025.06.27 14:19:42 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2-sources.jar (646 kB at 8.2 MB/s)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: commons-collections:commons-collections:jar:3.2.2:compile (sources = true)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-databind:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-databind:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-databind:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:42 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2-sources.jar
2025.06.27 14:19:42 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2-sources.jar
2025.06.27 14:19:42 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2-sources.jar
2025.06.27 14:19:42 INFO  Progress (1): 0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.2/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.2/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.2/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.2/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.2/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.2/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.3/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.3/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.3/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.3/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.3/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.3/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.4/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.4/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.4/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.4/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.4/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.4/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.5/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.5/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.5/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.5/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.5/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.5/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.6/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.6/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.6/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.6/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.6/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.6/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.7/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.7/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.7/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.7/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.7/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.7/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.8/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.8/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.8/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.8/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.8/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.8/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.9/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.9/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.9/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.9/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.9/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.9/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 0.9/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.0/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.1/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.2/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.2/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.2/1.2 MB
2025.06.27 14:19:42 INFO  Progress (1): 1.2 MB    
2025.06.27 14:19:42 INFO                      
2025.06.27 14:19:42 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2-sources.jar (1.2 MB at 8.6 MB/s)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-databind:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: com.twitter.common:util-system-mocks:jar:0.0.67:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: com.twitter.common:util-system-mocks:jar:0.0.67:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: com.twitter.common:util-system-mocks:jar:0.0.67:compile (sources = true)
2025.06.27 14:19:42 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/util-system-mocks/0.0.67/util-system-mocks-0.0.67-sources.jar
2025.06.27 14:19:42 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/util-system-mocks/0.0.67/util-system-mocks-0.0.67-sources.jar
2025.06.27 14:19:42 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/util-system-mocks/0.0.67/util-system-mocks-0.0.67-sources.jar
2025.06.27 14:19:42 INFO  Progress (1): 2.6 kB
2025.06.27 14:19:42 INFO                      
2025.06.27 14:19:42 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/util-system-mocks/0.0.67/util-system-mocks-0.0.67-sources.jar (2.6 kB at 107 kB/s)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: com.twitter.common:util-system-mocks:jar:0.0.67:compile (sources = true)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:42 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:42 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-metric-lib/2.1.13/galaxy-lcs-metric-lib-2.1.13-sources.jar
2025.06.27 14:19:42 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-metric-lib/2.1.13/galaxy-lcs-metric-lib-2.1.13-sources.jar
2025.06.27 14:19:42 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-metric-lib/2.1.13/galaxy-lcs-metric-lib-2.1.13-sources.jar
2025.06.27 14:19:44 INFO  Downloading from public: https://pkgs.d.xiaomi.net/artifactory/maven-public-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-metric-lib/2.1.13/galaxy-lcs-metric-lib-2.1.13-sources.jar
2025.06.27 14:19:44 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:44 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:sources:2.1.13 (absent): Could not find artifact com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:sources:2.1.13 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:44 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:44 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:44 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:44 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:44 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:44 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:44 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:44 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:44 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:44 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:44 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:44 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:44 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:44 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:44 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:44 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:44 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:44 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:44 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:44 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:44 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: Could not find artifact com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:sources:2.1.13 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.connector.basic.ArtifactTransportListener.transferFailed (ArtifactTransportListener.java:42)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector$TaskRunner.run (BasicRepositoryConnector.java:417)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector.get (BasicRepositoryConnector.java:260)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:537)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:44 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:44 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:44 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:44 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:44 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:44 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:44 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:44 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:44 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:44 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:44 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:44 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:44 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:44 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:44 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:44 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:44 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:44 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:44 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:44 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:44 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:44 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:44 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:44 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:44 INFO  [INFO] Resolving artifact: com.google.guava:guava:jar:11.0.2:compile (sources = false)
2025.06.27 14:19:44 INFO  [INFO] SUCCESS: com.google.guava:guava:jar:11.0.2:compile (sources = false)
2025.06.27 14:19:44 INFO  [INFO] Resolving artifact: com.google.guava:guava:jar:11.0.2:compile (sources = true)
2025.06.27 14:19:44 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/google/guava/guava/11.0.2/guava-11.0.2-sources.jar
2025.06.27 14:19:44 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/google/guava/guava/11.0.2/guava-11.0.2-sources.jar
2025.06.27 14:19:44 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/google/guava/guava/11.0.2/guava-11.0.2-sources.jar
2025.06.27 14:19:44 INFO  Progress (1): 7.4/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 16/944 kB 
2025.06.27 14:19:44 INFO  Progress (1): 32/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 48/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 65/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 81/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 97/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 114/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 130/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 147/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 163/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 179/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 196/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 212/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 229/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 245/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 261/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 278/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 294/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 310/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 327/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 343/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 360/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 376/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 392/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 409/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 425/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 442/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 458/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 474/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 491/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 507/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 523/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 540/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 556/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 573/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 589/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 605/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 622/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 638/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 655/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 671/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 687/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 704/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 720/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 736/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 753/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 769/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 786/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 802/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 818/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 835/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 851/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 868/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 884/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 900/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 917/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 933/944 kB
2025.06.27 14:19:44 INFO  Progress (1): 944 kB    
2025.06.27 14:19:44 INFO                      
2025.06.27 14:19:44 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/google/guava/guava/11.0.2/guava-11.0.2-sources.jar (944 kB at 8.2 MB/s)
2025.06.27 14:19:44 INFO  [INFO] SUCCESS: com.google.guava:guava:jar:11.0.2:compile (sources = true)
2025.06.27 14:19:44 INFO  [INFO] Resolving artifact: com.twitter.common:application-action:jar:0.0.66:compile (sources = false)
2025.06.27 14:19:44 INFO  [INFO] SUCCESS: com.twitter.common:application-action:jar:0.0.66:compile (sources = false)
2025.06.27 14:19:44 INFO  [INFO] Resolving artifact: com.twitter.common:application-action:jar:0.0.66:compile (sources = true)
2025.06.27 14:19:44 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/application-action/0.0.66/application-action-0.0.66-sources.jar
2025.06.27 14:19:44 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/application-action/0.0.66/application-action-0.0.66-sources.jar
2025.06.27 14:19:44 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/application-action/0.0.66/application-action-0.0.66-sources.jar
2025.06.27 14:19:44 INFO  Progress (1): 6.2 kB
2025.06.27 14:19:44 INFO                      
2025.06.27 14:19:44 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/application-action/0.0.66/application-action-0.0.66-sources.jar (6.2 kB at 214 kB/s)
2025.06.27 14:19:44 INFO  [INFO] SUCCESS: com.twitter.common:application-action:jar:0.0.66:compile (sources = true)
2025.06.27 14:19:44 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:streaming-http:jar:1.0:compile (sources = false)
2025.06.27 14:19:44 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:streaming-http:jar:1.0:compile (sources = false)
2025.06.27 14:19:44 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:streaming-http:jar:1.0:compile (sources = true)
2025.06.27 14:19:44 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/streaming-http/1.0/streaming-http-1.0-sources.jar
2025.06.27 14:19:44 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/infra/galaxy/streaming-http/1.0/streaming-http-1.0-sources.jar
2025.06.27 14:19:44 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/infra/galaxy/streaming-http/1.0/streaming-http-1.0-sources.jar
2025.06.27 14:19:45 INFO  no build target found for D:\code\feature-metasdk\src\main\scala\com\xiaomi\hdfsUtils\hdfsReader.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.27 14:19:45 INFO  Downloading from public: https://pkgs.d.xiaomi.net/artifactory/maven-public-virtual/com/xiaomi/infra/galaxy/streaming-http/1.0/streaming-http-1.0-sources.jar
2025.06.27 14:19:45 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:streaming-http:jar:1.0:compile (sources = true)
2025.06.27 14:19:45 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:streaming-http:jar:sources:1.0 (absent): Could not find artifact com.xiaomi.infra.galaxy:streaming-http:jar:sources:1.0 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:45 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:45 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:45 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:45 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:45 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:45 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:45 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:45 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: Could not find artifact com.xiaomi.infra.galaxy:streaming-http:jar:sources:1.0 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.connector.basic.ArtifactTransportListener.transferFailed (ArtifactTransportListener.java:42)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector$TaskRunner.run (BasicRepositoryConnector.java:417)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector.get (BasicRepositoryConnector.java:260)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:537)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:45 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:45 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:45 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:45 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:45 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:45 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:45 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:45 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:45 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:45 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:45 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-thrift/2.1.13/galaxy-lcs-thrift-2.1.13-sources.jar
2025.06.27 14:19:45 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-thrift/2.1.13/galaxy-lcs-thrift-2.1.13-sources.jar
2025.06.27 14:19:45 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-thrift/2.1.13/galaxy-lcs-thrift-2.1.13-sources.jar
2025.06.27 14:19:45 INFO  Downloading from public: https://pkgs.d.xiaomi.net/artifactory/maven-public-virtual/com/xiaomi/infra/galaxy/galaxy-lcs-thrift/2.1.13/galaxy-lcs-thrift-2.1.13-sources.jar
2025.06.27 14:19:45 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:45 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:sources:2.1.13 (absent): Could not find artifact com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:sources:2.1.13 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:45 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:45 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:45 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:45 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:45 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:45 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:45 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:45 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: Could not find artifact com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:sources:2.1.13 in releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.connector.basic.ArtifactTransportListener.transferFailed (ArtifactTransportListener.java:42)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector$TaskRunner.run (BasicRepositoryConnector.java:417)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.connector.basic.BasicRepositoryConnector.get (BasicRepositoryConnector.java:260)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:537)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:45 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:45 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:45 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:45 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:298)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:45 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:45 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:45 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:45 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:45 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:45 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:45 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:45 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:45 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:45 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:45 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:45 INFO  [INFO] Resolving artifact: org.json:json:jar:20090211:compile (sources = false)
2025.06.27 14:19:45 INFO  [INFO] SUCCESS: org.json:json:jar:20090211:compile (sources = false)
2025.06.27 14:19:45 INFO  [INFO] Resolving artifact: org.json:json:jar:20090211:compile (sources = true)
2025.06.27 14:19:45 INFO  [INFO] SUCCESS: org.json:json:jar:20090211:compile (sources = true)
2025.06.27 14:19:45 INFO  [INFO] Resolving artifact: com.twitter.common:util-sampler:jar:0.0.50:compile (sources = false)
2025.06.27 14:19:45 INFO  [INFO] SUCCESS: com.twitter.common:util-sampler:jar:0.0.50:compile (sources = false)
2025.06.27 14:19:45 INFO  [INFO] Resolving artifact: com.twitter.common:util-sampler:jar:0.0.50:compile (sources = true)
2025.06.27 14:19:45 INFO  Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/twitter/common/util-sampler/0.0.50/util-sampler-0.0.50-sources.jar
2025.06.27 14:19:45 INFO  Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/twitter/common/util-sampler/0.0.50/util-sampler-0.0.50-sources.jar
2025.06.27 14:19:45 INFO  Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/util-sampler/0.0.50/util-sampler-0.0.50-sources.jar
2025.06.27 14:19:45 INFO  Progress (1): 1.4 kB
2025.06.27 14:19:45 INFO                      
2025.06.27 14:19:45 INFO  Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/twitter/common/util-sampler/0.0.50/util-sampler-0.0.50-sources.jar (1.4 kB at 50 kB/s)
2025.06.27 14:19:45 INFO  [INFO] SUCCESS: com.twitter.common:util-sampler:jar:0.0.50:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Generated .bloop\dependencyProducer.json
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:stats:jar:0.0.89:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:stats:jar:0.0.89:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:stats:jar:0.0.89:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:stats:jar:0.0.89:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.thrift:libthrift:jar:0.15.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.thrift:libthrift:jar:0.15.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.thrift:libthrift:jar:0.15.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.thrift:libthrift:jar:0.15.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.protobuf:protobuf-java:jar:2.5.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.google.protobuf:protobuf-java:jar:2.5.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.protobuf:protobuf-java:jar:2.5.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.google.protobuf:protobuf-java:jar:2.5.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:stats-provider:jar:0.0.53:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:stats-provider:jar:0.0.53:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:stats-provider:jar:0.0.53:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:stats-provider:jar:0.0.53:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:stat:jar:0.0.26:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:stat:jar:0.0.26:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:stat:jar:0.0.26:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:stat:jar:0.0.26:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: aopalliance:aopalliance:jar:1.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: aopalliance:aopalliance:jar:1.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: aopalliance:aopalliance:jar:1.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: aopalliance:aopalliance:jar:1.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.lz4:lz4-java:jar:1.8.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.lz4:lz4-java:jar:1.8.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.lz4:lz4-java:jar:1.8.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.lz4:lz4-java:jar:1.8.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-simple:jar:1.7.36:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.slf4j:slf4j-simple:jar:1.7.36:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-simple:jar:1.7.36:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.slf4j:slf4j-simple:jar:1.7.36:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.commons:commons-compress:jar:1.8.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.commons:commons-compress:jar:1.8.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.commons:commons-compress:jar:1.8.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.commons:commons-compress:jar:1.8.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.data:data-platform-spec-feeds:jar:0.0.1-SNAPSHOT:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.data:data-platform-spec-feeds:jar:0.0.1-SNAPSHOT:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.data:data-platform-spec-feeds:jar:0.0.1-SNAPSHOT:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.data:data-platform-spec-feeds:jar:0.0.1-SNAPSHOT:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.httpcomponents:httpclient:jar:4.2.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.httpcomponents:httpclient:jar:4.2.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.httpcomponents:httpclient:jar:4.2.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.httpcomponents:httpclient:jar:4.2.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.avro:avro:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.avro:avro:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.avro:avro:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.avro:avro:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:collections:jar:0.0.69:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:collections:jar:0.0.69:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:collections:jar:0.0.69:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:collections:jar:0.0.69:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.avro:avro-protobuf:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.avro:avro-protobuf:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.avro:avro-protobuf:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.avro:avro-protobuf:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.2.8:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.2.8:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.2.8:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.2.8:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:jdk-logging:jar:0.0.41:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:jdk-logging:jar:0.0.41:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:jdk-logging:jar:0.0.41:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:jdk-logging:jar:0.0.41:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:util-executor-service-shutdown:jar:0.0.47:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:util-executor-service-shutdown:jar:0.0.47:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:util-executor-service-shutdown:jar:0.0.47:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:util-executor-service-shutdown:jar:0.0.47:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.tukaani:xz:jar:1.5:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.tukaani:xz:jar:1.5:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.tukaani:xz:jar:1.5:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.tukaani:xz:jar:1.5:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: javax.annotation:javax.annotation-api:jar:1.3.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: javax.annotation:javax.annotation-api:jar:1.3.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: javax.annotation:javax.annotation-api:jar:1.3.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: javax.annotation:javax.annotation-api:jar:1.3.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: dnsjava:dnsjava:jar:2.1.8:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: dnsjava:dnsjava:jar:2.1.8:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: dnsjava:dnsjava:jar:2.1.8:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: dnsjava:dnsjava:jar:2.1.8:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.inject:guice:jar:3.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.google.inject:guice:jar:3.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.inject:guice:jar:3.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.google.inject:guice:jar:3.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-logging:commons-logging:jar:1.1.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-logging:commons-logging:jar:1.1.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-logging:commons-logging:jar:1.1.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-logging:commons-logging:jar:1.1.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-log4j12:jar:1.6.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.slf4j:slf4j-log4j12:jar:1.6.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-log4j12:jar:1.6.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.slf4j:slf4j-log4j12:jar:1.6.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.thoughtworks.paranamer:paranamer:jar:2.7:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.thoughtworks.paranamer:paranamer:jar:2.7:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.thoughtworks.paranamer:paranamer:jar:2.7:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.thoughtworks.paranamer:paranamer:jar:2.7:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: javax.servlet:javax.servlet-api:jar:3.0.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: javax.servlet:javax.servlet-api:jar:3.0.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: javax.servlet:javax.servlet-api:jar:3.0.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: javax.servlet:javax.servlet-api:jar:3.0.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:46 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:46 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:sources:2.1.13 (absent): com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:sources:2.1.13 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:sources:2.1.13 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.newException (DefaultUpdateCheckManager.java:225)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.checkArtifact (DefaultUpdateCheckManager.java:188)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.gatherDownloads (DefaultArtifactResolver.java:586)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:525)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:quantity:jar:0.0.66:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:quantity:jar:0.0.66:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:quantity:jar:0.0.66:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:quantity:jar:0.0.66:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:1.0.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:1.0.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:1.0.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:1.0.0:compile (sources = true)
2025.06.27 14:19:46 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:sources:1.0.0 (absent): com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:sources:1.0.0 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:sources:1.0.0 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.newException (DefaultUpdateCheckManager.java:225)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.checkArtifact (DefaultUpdateCheckManager.java:188)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.gatherDownloads (DefaultArtifactResolver.java:586)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:525)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.codahale.metrics:metrics-core:jar:3.0.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.codahale.metrics:metrics-core:jar:3.0.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.codahale.metrics:metrics-core:jar:3.0.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.codahale.metrics:metrics-core:jar:3.0.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.code.gson:gson:jar:2.2.4:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.google.code.gson:gson:jar:2.2.4:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.code.gson:gson:jar:2.2.4:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.google.code.gson:gson:jar:2.2.4:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.github.luben:zstd-jni:jar:1.5.0-4:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.github.luben:zstd-jni:jar:1.5.0-4:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.github.luben:zstd-jni:jar:1.5.0-4:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.github.luben:zstd-jni:jar:1.5.0-4:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:stat-registry:jar:0.0.24:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:stat-registry:jar:0.0.24:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:stat-registry:jar:0.0.24:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:stat-registry:jar:0.0.24:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.commons:commons-lang3:jar:3.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.commons:commons-lang3:jar:3.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.commons:commons-lang3:jar:3.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.commons:commons-lang3:jar:3.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.fasterxml.uuid:java-uuid-generator:jar:3.1.3:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.fasterxml.uuid:java-uuid-generator:jar:3.1.3:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.fasterxml.uuid:java-uuid-generator:jar:3.1.3:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.fasterxml.uuid:java-uuid-generator:jar:3.1.3:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:base:jar:0.0.82:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:base:jar:0.0.82:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:base:jar:0.0.82:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:base:jar:0.0.82:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.code.findbugs:jsr305:jar:1.3.9:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.google.code.findbugs:jsr305:jar:1.3.9:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.code.findbugs:jsr305:jar:1.3.9:compile (sources = true)
2025.06.27 14:19:46 INFO  [ERROR] FAILURE com.google.code.findbugs:jsr305:jar:1.3.9:compile (sources = true)
2025.06.27 14:19:46 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.google.code.findbugs:jsr305:jar:sources:1.3.9 (absent): com.google.code.findbugs:jsr305:jar:sources:1.3.9 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: com.google.code.findbugs:jsr305:jar:sources:1.3.9 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.newException (DefaultUpdateCheckManager.java:225)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.checkArtifact (DefaultUpdateCheckManager.java:188)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.gatherDownloads (DefaultArtifactResolver.java:586)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:525)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.httpcomponents:httpcore:jar:4.2.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.httpcomponents:httpcore:jar:4.2.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.httpcomponents:httpcore:jar:4.2.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.httpcomponents:httpcore:jar:4.2.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-talos-sdk:jar:2.7.0.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-talos-sdk:jar:2.7.0.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-talos-sdk:jar:2.7.0.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-talos-sdk:jar:2.7.0.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-annotations:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-annotations:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-annotations:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-annotations:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-lang:commons-lang:jar:2.6:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-lang:commons-lang:jar:2.6:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-lang:commons-lang:jar:2.6:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-lang:commons-lang:jar:2.6:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-io:commons-io:jar:2.11.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-io:commons-io:jar:2.11.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-io:commons-io:jar:2.11.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-io:commons-io:jar:2.11.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.thrift:thrift:jar:0.5.0-fix-thrift2402:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.thrift:thrift:jar:0.5.0-fix-thrift2402:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.thrift:thrift:jar:0.5.0-fix-thrift2402:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.thrift:thrift:jar:0.5.0-fix-thrift2402:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-client-java:jar:1.2.5:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-client-java:jar:1.2.5:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-client-java:jar:1.2.5:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-client-java:jar:1.2.5:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-core:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-core:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-core:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-core:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.avro:avro-thrift:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.avro:avro-thrift:jar:1.8.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.apache.avro:avro-thrift:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.apache.avro:avro-thrift:jar:1.8.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.jsoup:jsoup:jar:1.13.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.jsoup:jsoup:jar:1.13.1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.jsoup:jsoup:jar:1.13.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.jsoup:jsoup:jar:1.13.1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.xerial.snappy:snappy-java:jar:1.1.2.6:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.xerial.snappy:snappy-java:jar:1.1.2.6:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.xerial.snappy:snappy-java:jar:1.1.2.6:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.xerial.snappy:snappy-java:jar:1.1.2.6:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-schema-client:jar:1.0.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-schema-client:jar:1.0.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-schema-client:jar:1.0.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-schema-client:jar:1.0.0:compile (sources = true)
2025.06.27 14:19:46 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-schema-client:jar:sources:1.0.0 (absent): com.xiaomi.infra.galaxy:galaxy-schema-client:jar:sources:1.0.0 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: com.xiaomi.infra.galaxy:galaxy-schema-client:jar:sources:1.0.0 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.newException (DefaultUpdateCheckManager.java:225)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.checkArtifact (DefaultUpdateCheckManager.java:188)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.gatherDownloads (DefaultArtifactResolver.java:586)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:525)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-codec:commons-codec:jar:1.4:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-codec:commons-codec:jar:1.4:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-codec:commons-codec:jar:1.4:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-codec:commons-codec:jar:1.4:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-beanutils:commons-beanutils:jar:1.9.4:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-beanutils:commons-beanutils:jar:1.9.4:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-beanutils:commons-beanutils:jar:1.9.4:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-beanutils:commons-beanutils:jar:1.9.4:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: log4j:log4j:jar:1.2.16:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: log4j:log4j:jar:1.2.16:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: log4j:log4j:jar:1.2.16:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: log4j:log4j:jar:1.2.16:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: javax.inject:javax.inject:jar:1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: javax.inject:javax.inject:jar:1:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: javax.inject:javax.inject:jar:1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: javax.inject:javax.inject:jar:1:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:streaming-common:jar:1.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:streaming-common:jar:1.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:streaming-common:jar:1.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:streaming-common:jar:1.0:compile (sources = true)
2025.06.27 14:19:46 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:streaming-common:jar:sources:1.0 (absent): com.xiaomi.infra.galaxy:streaming-common:jar:sources:1.0 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: com.xiaomi.infra.galaxy:streaming-common:jar:sources:1.0 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.newException (DefaultUpdateCheckManager.java:225)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.checkArtifact (DefaultUpdateCheckManager.java:188)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.gatherDownloads (DefaultArtifactResolver.java:586)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:525)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-api:jar:1.7.28:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.slf4j:slf4j-api:jar:1.7.28:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.slf4j:slf4j-api:jar:1.7.28:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.slf4j:slf4j-api:jar:1.7.28:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:util:jar:0.0.92:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:util:jar:0.0.92:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:util:jar:0.0.92:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:util:jar:0.0.92:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-collections:commons-collections:jar:3.2.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-collections:commons-collections:jar:3.2.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: commons-collections:commons-collections:jar:3.2.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: commons-collections:commons-collections:jar:3.2.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-databind:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-databind:jar:2.15.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.fasterxml.jackson.core:jackson-databind:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.fasterxml.jackson.core:jackson-databind:jar:2.15.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:util-system-mocks:jar:0.0.67:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:util-system-mocks:jar:0.0.67:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:util-system-mocks:jar:0.0.67:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:util-system-mocks:jar:0.0.67:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:46 INFO  no build target found for D:\code\feature-metasdk\src\main\scala\com\xiaomi\hdfsUtils\hdfsReader.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.27 14:19:46 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:46 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:sources:2.1.13 (absent): com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:sources:2.1.13 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 WARN  no build target for: D:\code\feature-metasdk\src\main\scala\com\xiaomi\hdfsUtils\hdfsReader.scala
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:sources:2.1.13 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.newException (DefaultUpdateCheckManager.java:225)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.checkArtifact (DefaultUpdateCheckManager.java:188)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.gatherDownloads (DefaultArtifactResolver.java:586)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:525)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.guava:guava:jar:11.0.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.google.guava:guava:jar:11.0.2:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.google.guava:guava:jar:11.0.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.google.guava:guava:jar:11.0.2:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:application-action:jar:0.0.66:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:application-action:jar:0.0.66:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:application-action:jar:0.0.66:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:application-action:jar:0.0.66:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:streaming-http:jar:1.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:streaming-http:jar:1.0:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:streaming-http:jar:1.0:compile (sources = true)
2025.06.27 14:19:46 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:streaming-http:jar:1.0:compile (sources = true)
2025.06.27 14:19:46 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:streaming-http:jar:sources:1.0 (absent): com.xiaomi.infra.galaxy:streaming-http:jar:sources:1.0 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: com.xiaomi.infra.galaxy:streaming-http:jar:sources:1.0 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.newException (DefaultUpdateCheckManager.java:225)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.checkArtifact (DefaultUpdateCheckManager.java:188)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.gatherDownloads (DefaultArtifactResolver.java:586)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:525)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:2.1.13:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:46 INFO  [ERROR] FAILURE com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:2.1.13:compile (sources = true)
2025.06.27 14:19:46 INFO  org.eclipse.aether.resolution.ArtifactResolutionException: The following artifacts could not be resolved: com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:sources:2.1.13 (absent): com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:sources:2.1.13 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:473)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  Caused by: org.eclipse.aether.transfer.ArtifactNotFoundException: com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:sources:2.1.13 was not found in https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of releases has elapsed or updates are forced
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.newException (DefaultUpdateCheckManager.java:225)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultUpdateCheckManager.checkArtifact (DefaultUpdateCheckManager.java:188)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.gatherDownloads (DefaultArtifactResolver.java:586)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.performDownloads (DefaultArtifactResolver.java:525)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolve (DefaultArtifactResolver.java:449)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifacts (DefaultArtifactResolver.java:261)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultArtifactResolver.resolveArtifact (DefaultArtifactResolver.java:243)
2025.06.27 14:19:46 INFO      at org.eclipse.aether.internal.impl.DefaultRepositorySystem.resolveArtifact (DefaultRepositorySystem.java:278)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.bloop$integrations$maven$MojoImplementation$$resolveArtifact$1 (MojoImplementation.scala:115)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:240)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$$anonfun$2.applyOrElse (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect (StrictOptimizedIterableOps.scala:151)
2025.06.27 14:19:46 INFO      at scala.collection.StrictOptimizedIterableOps.collect$ (StrictOptimizedIterableOps.scala:136)
2025.06.27 14:19:46 INFO      at scala.collection.mutable.HashSet.collect (HashSet.scala:31)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeConfig$1 (MojoImplementation.scala:222)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation$.writeCompileAndTestConfiguration (MojoImplementation.scala:307)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.MojoImplementation.writeCompileAndTestConfiguration (MojoImplementation.scala)
2025.06.27 14:19:46 INFO      at bloop.integrations.maven.BloopMojo.execute (BloopMojo.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
2025.06.27 14:19:46 INFO      at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
2025.06.27 14:19:46 INFO      at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
2025.06.27 14:19:46 INFO      at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
2025.06.27 14:19:46 INFO      at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
2025.06.27 14:19:46 INFO      at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0 (Native Method)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.NativeMethodAccessorImpl.invoke (NativeMethodAccessorImpl.java:77)
2025.06.27 14:19:46 INFO      at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke (DelegatingMethodAccessorImpl.java:43)
2025.06.27 14:19:46 INFO      at java.lang.reflect.Method.invoke (Method.java:569)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.BootstrapMainStarter.start (BootstrapMainStarter.java:52)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.WrapperExecutor.execute (WrapperExecutor.java:161)
2025.06.27 14:19:46 INFO      at org.apache.maven.wrapper.MavenWrapperMain.main (MavenWrapperMain.java:73)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.json:json:jar:20090211:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.json:json:jar:20090211:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: org.json:json:jar:20090211:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: org.json:json:jar:20090211:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:util-sampler:jar:0.0.50:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:util-sampler:jar:0.0.50:compile (sources = false)
2025.06.27 14:19:46 INFO  [INFO] Resolving artifact: com.twitter.common:util-sampler:jar:0.0.50:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] SUCCESS: com.twitter.common:util-sampler:jar:0.0.50:compile (sources = true)
2025.06.27 14:19:46 INFO  [INFO] Generated .bloop\dependencyProducer-test.json
2025.06.27 14:19:46 INFO  [INFO] ------------------------------------------------------------------------
2025.06.27 14:19:46 INFO  [INFO] BUILD SUCCESS
2025.06.27 14:19:46 INFO  [INFO] ------------------------------------------------------------------------
2025.06.27 14:19:46 INFO  no build target found for D:\code\feature-metasdk\src\main\scala\com\xiaomi\hdfsUtils\hdfsReader.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.27 14:19:46 INFO  [INFO] Total time:  16.221 s
2025.06.27 14:19:46 INFO  [INFO] Finished at: 2025-06-27T14:19:46+08:00
2025.06.27 14:19:46 INFO  [INFO] ------------------------------------------------------------------------
2025.06.27 14:19:46 INFO  time: ran 'mvn bloopInstall' in 17s
2025.06.27 14:19:47 INFO  Attempting to connect to the build server...
2025.06.27 14:19:47 INFO  No running Bloop server found, starting one.
2025.06.27 14:19:48 INFO  Starting compilation server
2025.06.27 14:19:51 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at D:\code\feature-metasdk\.metals\bsp.trace.json or C:\Users\<USER>\AppData\Local\scalameta\metals\cache\bsp.trace.json
2025.06.27 14:19:52 INFO  time: Connected to build server in 5.22s
2025.06.27 14:19:51 INFO  Connected to Build server: Bloop v2.0.10
2025.06.27 14:19:52 INFO  time: Imported build in 0.12s
2025.06.27 14:19:56 WARN  corrupted jar C:\Users\<USER>\.m2\repository\org\json\json\20090211\json-20090211-sources.jar: java.util.zip.ZipException: zip END header not found
2025.06.27 14:19:56 WARN  invalid jar: C:\Users\<USER>\.m2\repository\org\json\json\20090211\json-20090211-sources.jar
java.util.zip.ZipException: zip END header not found
	at jdk.nio.zipfs.ZipFileSystem.findEND(ZipFileSystem.java:1320)
	at jdk.nio.zipfs.ZipFileSystem.initCEN(ZipFileSystem.java:1534)
	at jdk.nio.zipfs.ZipFileSystem.<init>(ZipFileSystem.java:179)
	at jdk.nio.zipfs.ZipFileSystemProvider.getZipFileSystem(ZipFileSystemProvider.java:125)
	at jdk.nio.zipfs.ZipFileSystemProvider.newFileSystem(ZipFileSystemProvider.java:106)
	at java.nio.file.FileSystems.newFileSystem(FileSystems.java:339)
	at java.nio.file.FileSystems.newFileSystem(FileSystems.java:288)
	at scala.meta.internal.io.PlatformFileIO$.newFileSystem(PlatformFileIO.scala:91)
	at scala.meta.internal.io.PlatformFileIO$.newJarFileSystem(PlatformFileIO.scala:87)
	at scala.meta.internal.io.PlatformFileIO$.withJarFileSystem(PlatformFileIO.scala:69)
	at scala.meta.internal.io.FileIO$.withJarFileSystem(FileIO.scala:33)
	at scala.meta.internal.mtags.SymbolIndexBucket.addSourceJar(SymbolIndexBucket.scala:68)
	at scala.meta.internal.mtags.OnDemandSymbolIndex.$anonfun$addSourceJar$2(OnDemandSymbolIndex.scala:85)
	at scala.meta.internal.mtags.OnDemandSymbolIndex.tryRun(OnDemandSymbolIndex.scala:131)
	at scala.meta.internal.mtags.OnDemandSymbolIndex.addSourceJar(OnDemandSymbolIndex.scala:84)
	at scala.meta.internal.metals.Indexer.indexJar(Indexer.scala:565)
	at scala.meta.internal.metals.Indexer.addSourceJarSymbols(Indexer.scala:559)
	at scala.meta.internal.metals.Indexer.$anonfun$indexDependencySources$5(Indexer.scala:387)
	at scala.collection.IterableOnceOps.foreach(IterableOnce.scala:619)
	at scala.collection.IterableOnceOps.foreach$(IterableOnce.scala:617)
	at scala.collection.AbstractIterable.foreach(Iterable.scala:935)
	at scala.collection.IterableOps$WithFilter.foreach(Iterable.scala:905)
	at scala.meta.internal.metals.Indexer.$anonfun$indexDependencySources$1(Indexer.scala:378)
	at scala.meta.internal.metals.Indexer.$anonfun$indexDependencySources$1$adapted(Indexer.scala:377)
	at scala.collection.IterableOnceOps.foreach(IterableOnce.scala:619)
	at scala.collection.IterableOnceOps.foreach$(IterableOnce.scala:617)
	at scala.collection.AbstractIterable.foreach(Iterable.scala:935)
	at scala.meta.internal.metals.Indexer.indexDependencySources(Indexer.scala:377)
	at scala.meta.internal.metals.Indexer.$anonfun$indexWorkspace$20(Indexer.scala:198)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.meta.internal.metals.TimerProvider.timedThunk(TimerProvider.scala:25)
	at scala.meta.internal.metals.Indexer.$anonfun$indexWorkspace$19(Indexer.scala:191)
	at scala.meta.internal.metals.Indexer.$anonfun$indexWorkspace$19$adapted(Indexer.scala:187)
	at scala.collection.immutable.List.foreach(List.scala:334)
	at scala.meta.internal.metals.Indexer.indexWorkspace(Indexer.scala:187)
	at scala.meta.internal.metals.Indexer.$anonfun$profiledIndexWorkspace$2(Indexer.scala:57)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.meta.internal.metals.TimerProvider.timedThunk(TimerProvider.scala:25)
	at scala.meta.internal.metals.Indexer.$anonfun$profiledIndexWorkspace$1(Indexer.scala:57)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.concurrent.Future$.$anonfun$apply$1(Future.scala:687)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:467)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(Thread.java:840)

2025.06.27 14:19:56 INFO  time: indexed workspace in 4.98s
2025.06.27 14:19:57 INFO  compiling dependencyproducer (1 scala source and 3 java sources)
2025.06.27 14:20:00 INFO  Non-compiled module 'compiler-bridge_2.12' for Scala 2.12.20. Compiling...
2025.06.27 14:20:14 INFO  Compilation completed in 14.214s.
2025.06.27 14:20:15 INFO  Created report: Optional.empty
2025.06.27 14:20:15 INFO  error while loading Object, Missing dependency 'object scala.native in compiler mirror', required by... (full report at: "D:\code\feature-metasdk\.metals\.reports\bloop\2025-06-27\r_error while loading ..._14-20-15-936.md")
2025.06.27 14:20:15 INFO  time: compiled dependencyProducer in 18s
2025.06.27 14:20:15 INFO  Created report: Optional.empty
2025.06.27 14:20:15 INFO  scala.reflect.internal.MissingRequirementError: object scala in compiler mirror not found.
	at scal... (full report at: "D:\code\feature-metasdk\.metals\.reports\bloop\2025-06-27\r_scala.reflect.intern..._14-20-15-972.md")
2025.06.27 14:20:15 INFO  Created report: Optional.empty
2025.06.27 14:20:15 INFO  Unexpected error when compiling dependencyProducer: scala.reflect.internal.MissingRequirementError: ... (full report at: "D:\code\feature-metasdk\.metals\.reports\bloop\2025-06-27\r_Unexpected error whe..._14-20-15-978.md")
2025.06.27 14:20:27 INFO  compiling dependencyproducer (1 scala source and 3 java sources)
2025.06.27 14:20:27 INFO  error while loading Object, Missing dependency 'object scala.native in compiler mirror', required by... (full report at: "D:\code\feature-metasdk\.metals\.reports\bloop\2025-06-27\r_error while loading ..._14-20-15-936.md")
2025.06.27 14:20:27 INFO  time: compiled dependencyProducer in 0.14s
2025.06.27 14:20:27 INFO  scala.reflect.internal.MissingRequirementError: object scala in compiler mirror not found.
	at scal... (full report at: "D:\code\feature-metasdk\.metals\.reports\bloop\2025-06-27\r_scala.reflect.intern..._14-20-15-972.md")
2025.06.27 14:20:27 INFO  Unexpected error when compiling dependencyProducer: scala.reflect.internal.MissingRequirementError: ... (full report at: "D:\code\feature-metasdk\.metals\.reports\bloop\2025-06-27\r_Unexpected error whe..._14-20-15-978.md")
2025.06.27 15:03:23 INFO  compiling dependencyproducer (3 java sources)
2025.06.27 15:03:25 WARN  Unexpected javac output: ����: [options] δ�� -source 8 һ������������·��
1 ������.
2025.06.27 15:03:25 WARN  javac: [options] δ�� -source 8 һ������������·��
2025.06.27 15:03:25 INFO  time: compiled dependencyProducer in 2.83s
2025.06.27 16:08:05 INFO  Shutting down server
2025.06.27 16:08:05 INFO  shutting down Metals
2025.06.27 16:08:05 INFO  Shut down connection with build server.
2025.06.27 16:08:05 INFO  Exiting server
2025.06.27 16:11:17 INFO  Started: Metals version 1.6.0 in folders 'D:\code\feature-metasdk' for client Cursor 1.96.2.
2025.06.27 16:11:24 INFO  Attempting to connect to the build server...
2025.06.27 16:11:24 INFO  Found a Bloop server running
2025.06.27 16:11:24 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at D:\code\feature-metasdk\.metals\bsp.trace.json or C:\Users\<USER>\AppData\Local\scalameta\metals\cache\bsp.trace.json
2025.06.27 16:11:24 INFO  time: Connected to build server in 0.9s
2025.06.27 16:11:24 INFO  Connected to Build server: Bloop v2.0.10
2025.06.27 16:11:25 INFO  time: Imported build in 0.18s
2025.06.27 16:11:29 WARN  corrupted jar C:\Users\<USER>\.m2\repository\org\json\json\20090211\json-20090211-sources.jar: java.util.zip.ZipException: zip END header not found
2025.06.27 16:11:29 WARN  invalid jar: C:\Users\<USER>\.m2\repository\org\json\json\20090211\json-20090211-sources.jar
java.util.zip.ZipException: zip END header not found
	at jdk.nio.zipfs.ZipFileSystem.findEND(ZipFileSystem.java:1320)
	at jdk.nio.zipfs.ZipFileSystem.initCEN(ZipFileSystem.java:1534)
	at jdk.nio.zipfs.ZipFileSystem.<init>(ZipFileSystem.java:179)
	at jdk.nio.zipfs.ZipFileSystemProvider.getZipFileSystem(ZipFileSystemProvider.java:125)
	at jdk.nio.zipfs.ZipFileSystemProvider.newFileSystem(ZipFileSystemProvider.java:106)
	at java.nio.file.FileSystems.newFileSystem(FileSystems.java:339)
	at java.nio.file.FileSystems.newFileSystem(FileSystems.java:288)
	at scala.meta.internal.io.PlatformFileIO$.newFileSystem(PlatformFileIO.scala:91)
	at scala.meta.internal.io.PlatformFileIO$.newJarFileSystem(PlatformFileIO.scala:87)
	at scala.meta.internal.io.PlatformFileIO$.withJarFileSystem(PlatformFileIO.scala:69)
	at scala.meta.internal.io.FileIO$.withJarFileSystem(FileIO.scala:33)
	at scala.meta.internal.mtags.SymbolIndexBucket.addSourceJar(SymbolIndexBucket.scala:68)
	at scala.meta.internal.mtags.OnDemandSymbolIndex.$anonfun$addSourceJar$2(OnDemandSymbolIndex.scala:85)
	at scala.meta.internal.mtags.OnDemandSymbolIndex.tryRun(OnDemandSymbolIndex.scala:131)
	at scala.meta.internal.mtags.OnDemandSymbolIndex.addSourceJar(OnDemandSymbolIndex.scala:84)
	at scala.meta.internal.metals.Indexer.indexJar(Indexer.scala:565)
	at scala.meta.internal.metals.Indexer.addSourceJarSymbols(Indexer.scala:559)
	at scala.meta.internal.metals.Indexer.$anonfun$indexDependencySources$5(Indexer.scala:387)
	at scala.collection.IterableOnceOps.foreach(IterableOnce.scala:619)
	at scala.collection.IterableOnceOps.foreach$(IterableOnce.scala:617)
	at scala.collection.AbstractIterable.foreach(Iterable.scala:935)
	at scala.collection.IterableOps$WithFilter.foreach(Iterable.scala:905)
	at scala.meta.internal.metals.Indexer.$anonfun$indexDependencySources$1(Indexer.scala:378)
	at scala.meta.internal.metals.Indexer.$anonfun$indexDependencySources$1$adapted(Indexer.scala:377)
	at scala.collection.IterableOnceOps.foreach(IterableOnce.scala:619)
	at scala.collection.IterableOnceOps.foreach$(IterableOnce.scala:617)
	at scala.collection.AbstractIterable.foreach(Iterable.scala:935)
	at scala.meta.internal.metals.Indexer.indexDependencySources(Indexer.scala:377)
	at scala.meta.internal.metals.Indexer.$anonfun$indexWorkspace$20(Indexer.scala:198)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.meta.internal.metals.TimerProvider.timedThunk(TimerProvider.scala:25)
	at scala.meta.internal.metals.Indexer.$anonfun$indexWorkspace$19(Indexer.scala:191)
	at scala.meta.internal.metals.Indexer.$anonfun$indexWorkspace$19$adapted(Indexer.scala:187)
	at scala.collection.immutable.List.foreach(List.scala:334)
	at scala.meta.internal.metals.Indexer.indexWorkspace(Indexer.scala:187)
	at scala.meta.internal.metals.Indexer.$anonfun$profiledIndexWorkspace$2(Indexer.scala:57)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.meta.internal.metals.TimerProvider.timedThunk(TimerProvider.scala:25)
	at scala.meta.internal.metals.Indexer.$anonfun$profiledIndexWorkspace$1(Indexer.scala:57)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.concurrent.Future$.$anonfun$apply$1(Future.scala:687)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:467)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(Thread.java:840)

2025.06.27 16:11:29 INFO  time: indexed workspace in 4.48s
2025.06.27 16:11:37 INFO  Shutting down server
2025.06.27 16:11:37 INFO  shutting down Metals
2025.06.27 16:11:37 INFO  Shut down connection with build server.
2025.06.27 16:11:37 INFO  Exiting server
2025.06.27 17:13:23 INFO  Started: Metals version 1.6.0 in folders 'D:\code\feature-metasdk' for client Cursor 1.96.2.
2025.06.27 17:13:26 INFO  Attempting to connect to the build server...
2025.06.27 17:13:26 INFO  Found a Bloop server running
2025.06.27 17:13:26 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at D:\code\feature-metasdk\.metals\bsp.trace.json or C:\Users\<USER>\AppData\Local\scalameta\metals\cache\bsp.trace.json
2025.06.27 17:13:26 INFO  time: Connected to build server in 0.55s
2025.06.27 17:13:26 INFO  Connected to Build server: Bloop v2.0.10
2025.06.27 17:13:27 INFO  time: Imported build in 0.27s
2025.06.27 17:13:30 WARN  corrupted jar C:\Users\<USER>\.m2\repository\org\json\json\20090211\json-20090211-sources.jar: java.util.zip.ZipException: zip END header not found
2025.06.27 17:13:30 WARN  invalid jar: C:\Users\<USER>\.m2\repository\org\json\json\20090211\json-20090211-sources.jar
java.util.zip.ZipException: zip END header not found
	at jdk.nio.zipfs.ZipFileSystem.findEND(ZipFileSystem.java:1320)
	at jdk.nio.zipfs.ZipFileSystem.initCEN(ZipFileSystem.java:1534)
	at jdk.nio.zipfs.ZipFileSystem.<init>(ZipFileSystem.java:179)
	at jdk.nio.zipfs.ZipFileSystemProvider.getZipFileSystem(ZipFileSystemProvider.java:125)
	at jdk.nio.zipfs.ZipFileSystemProvider.newFileSystem(ZipFileSystemProvider.java:106)
	at java.nio.file.FileSystems.newFileSystem(FileSystems.java:339)
	at java.nio.file.FileSystems.newFileSystem(FileSystems.java:288)
	at scala.meta.internal.io.PlatformFileIO$.newFileSystem(PlatformFileIO.scala:91)
	at scala.meta.internal.io.PlatformFileIO$.newJarFileSystem(PlatformFileIO.scala:87)
	at scala.meta.internal.io.PlatformFileIO$.withJarFileSystem(PlatformFileIO.scala:69)
	at scala.meta.internal.io.FileIO$.withJarFileSystem(FileIO.scala:33)
	at scala.meta.internal.mtags.SymbolIndexBucket.addSourceJar(SymbolIndexBucket.scala:68)
	at scala.meta.internal.mtags.OnDemandSymbolIndex.$anonfun$addSourceJar$2(OnDemandSymbolIndex.scala:85)
	at scala.meta.internal.mtags.OnDemandSymbolIndex.tryRun(OnDemandSymbolIndex.scala:131)
	at scala.meta.internal.mtags.OnDemandSymbolIndex.addSourceJar(OnDemandSymbolIndex.scala:84)
	at scala.meta.internal.metals.Indexer.indexJar(Indexer.scala:565)
	at scala.meta.internal.metals.Indexer.addSourceJarSymbols(Indexer.scala:559)
	at scala.meta.internal.metals.Indexer.$anonfun$indexDependencySources$5(Indexer.scala:387)
	at scala.collection.IterableOnceOps.foreach(IterableOnce.scala:619)
	at scala.collection.IterableOnceOps.foreach$(IterableOnce.scala:617)
	at scala.collection.AbstractIterable.foreach(Iterable.scala:935)
	at scala.collection.IterableOps$WithFilter.foreach(Iterable.scala:905)
	at scala.meta.internal.metals.Indexer.$anonfun$indexDependencySources$1(Indexer.scala:378)
	at scala.meta.internal.metals.Indexer.$anonfun$indexDependencySources$1$adapted(Indexer.scala:377)
	at scala.collection.IterableOnceOps.foreach(IterableOnce.scala:619)
	at scala.collection.IterableOnceOps.foreach$(IterableOnce.scala:617)
	at scala.collection.AbstractIterable.foreach(Iterable.scala:935)
	at scala.meta.internal.metals.Indexer.indexDependencySources(Indexer.scala:377)
	at scala.meta.internal.metals.Indexer.$anonfun$indexWorkspace$20(Indexer.scala:198)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.meta.internal.metals.TimerProvider.timedThunk(TimerProvider.scala:25)
	at scala.meta.internal.metals.Indexer.$anonfun$indexWorkspace$19(Indexer.scala:191)
	at scala.meta.internal.metals.Indexer.$anonfun$indexWorkspace$19$adapted(Indexer.scala:187)
	at scala.collection.immutable.List.foreach(List.scala:334)
	at scala.meta.internal.metals.Indexer.indexWorkspace(Indexer.scala:187)
	at scala.meta.internal.metals.Indexer.$anonfun$profiledIndexWorkspace$2(Indexer.scala:57)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.meta.internal.metals.TimerProvider.timedThunk(TimerProvider.scala:25)
	at scala.meta.internal.metals.Indexer.$anonfun$profiledIndexWorkspace$1(Indexer.scala:57)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.concurrent.Future$.$anonfun$apply$1(Future.scala:687)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:467)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(Thread.java:840)

2025.06.27 17:13:32 INFO  time: indexed workspace in 4.56s
2025.06.30 09:42:47 INFO  Shutting down server
2025.06.30 09:42:47 INFO  shutting down Metals
2025.06.30 09:42:47 INFO  Shut down connection with build server.
2025.06.30 09:42:47 INFO  Exiting server
